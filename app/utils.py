"""
Utility functions for the PDF Information Extractor.
"""
import os
import tempfile
import tik<PERSON>en
from typing import List, Dict, Any, Optional


def save_uploaded_file(uploaded_file, directory: str = None) -> str:
    """
    Save an uploaded file to a temporary directory or specified directory.

    Args:
        uploaded_file: The uploaded file object
        directory: Optional directory to save the file to

    Returns:
        str: Path to the saved file
    """
    if directory is None:
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, uploaded_file.filename)
    else:
        os.makedirs(directory, exist_ok=True)
        file_path = os.path.join(directory, uploaded_file.filename)

    with open(file_path, "wb") as f:
        f.write(uploaded_file.file.read())

    return file_path


def chunk_text_data(text: str, chunk_size: int, overlap: int, page_markers: Optional[Dict[int, str]] = None) -> List[Dict[str, Any]]:
    """
    Split text into chunks based on token count with overlap.
    Also tracks page numbers if page markers are provided.

    Args:
        text: The text to split
        chunk_size: Maximum number of tokens per chunk
        overlap: Number of tokens to overlap between chunks
        page_markers: Dictionary mapping character positions to page numbers {char_position: page_number}

    Returns:
        List[Dict[str, Any]]: List of dictionaries containing:
            - text: The chunk text
            - pages: List of page numbers contained in this chunk
            - page_map: Dictionary mapping page numbers to text segments within the chunk
    """
    if not text:
        return []

    # Ensure chunk_size is reasonable
    if chunk_size <= 0:
        chunk_size = 1000  # Default to 1000 tokens if invalid

    # Ensure overlap is reasonable and less than chunk_size
    if overlap < 0 or overlap >= chunk_size:
        overlap = min(200, chunk_size // 4)  # Default to 200 or 25% of chunk_size

    # Initialize tiktoken encoder
    try:
        encoder = tiktoken.get_encoding("cl100k_base")  # Using the same encoding as GPT-4
    except Exception as e:
        print(f"[WARNING] Error loading tiktoken encoding: {str(e)}")
        print("[WARNING] Falling back to approximate token counting (4 chars = 1 token)")
        # Create a simple fallback encoder that approximates tokens (4 chars = 1 token)
        class FallbackEncoder:
            def encode(self, text):
                # Approximate tokens (4 chars = 1 token)
                return list(range(len(text) // 4))

            def decode(self, tokens):
                # For decoding, just return the original text slice
                if not tokens:
                    return ""
                if isinstance(tokens, list) and len(tokens) == 0:
                    return ""
                # If we're decoding a slice, estimate the character positions
                if isinstance(tokens, list) and hasattr(tokens, "__getitem__"):
                    start_char = 0
                    end_char = len(text)
                    if len(tokens) > 0:
                        # Approximate character positions
                        start_char = min(tokens[0] * 4, len(text))
                        end_char = min((tokens[-1] + 1) * 4, len(text))
                    return text[start_char:end_char]
                return text

        encoder = FallbackEncoder()

    # Tokenize the entire text
    tokens = encoder.encode(text)
    token_count = len(tokens)

    # Initialize variables for chunking
    chunks = []
    start_token = 0
    chunk_count = 0

    print(f"[INFO] Chunking text of {token_count} tokens into chunks of size {chunk_size} tokens with overlap {overlap} tokens")

    # Page separator marker for output
    PAGE_SEPARATOR = "===== PAGE {page_num} ====="

    while start_token < token_count:
        # Calculate the end token for this chunk based on chunk_size
        end_token = min(start_token + chunk_size, token_count)

        # Extract the chunk text
        chunk_text = encoder.decode(tokens[start_token:end_token])

        # Track page numbers if page markers are provided
        chunk_pages = []
        page_text_map = {}

        if page_markers:
            # Convert token positions to character positions
            start_char = len(encoder.decode(tokens[:start_token]))
            end_char = len(encoder.decode(tokens[:end_token]))

            # Find all page markers that fall within this chunk
            current_pages = []
            for pos, page in sorted(page_markers.items()):
                if start_char <= pos < end_char:
                    if page not in current_pages:
                        current_pages.append(page)
                        # Add page separator to the chunk text at the appropriate position
                        relative_pos = pos - start_char
                        if relative_pos > 0:  # Don't insert at the very beginning
                            insert_pos = min(relative_pos, len(chunk_text))
                            # Find the nearest newline before the insert position
                            nl_pos = chunk_text[:insert_pos].rfind('\n')
                            if nl_pos != -1:
                                insert_pos = nl_pos + 1

                            # Insert the page separator
                            page_separator = PAGE_SEPARATOR.format(page_num=page)
                            chunk_text = chunk_text[:insert_pos] + f"\n{page_separator}\n" + chunk_text[insert_pos:]

                            # Track the page and its text in the chunk
                            page_start = chunk_text.find(page_separator)
                            next_page_start = chunk_text.find(PAGE_SEPARATOR.format(page_num=page+1))
                            if next_page_start == -1:
                                next_page_start = len(chunk_text)

                            page_text = chunk_text[page_start:next_page_start].strip()
                            page_text_map[page] = page_text

            # Add the first page if no page markers were found in this chunk
            if not current_pages and page_markers:
                # Find the page that comes before the chunk start
                prev_pages = [page for pos, page in page_markers.items() if pos < start_char]
                if prev_pages:
                    current_page = max(prev_pages)
                    current_pages.append(current_page)
                    page_text_map[current_page] = chunk_text

            chunk_pages = current_pages

        # Create the chunk dictionary
        chunk_dict = {
            "text": chunk_text,
            "pages": chunk_pages,
            "page_map": page_text_map
        }

        chunks.append(chunk_dict)
        chunk_count += 1
        
        print(f"[INFO] Added chunk {chunk_count}: {len(tokens[start_token:end_token])} tokens, pages: {chunk_pages}")

        # Move start position for next chunk, considering overlap
        # Ensure we always make forward progress
        start_token = start_token + max(1, chunk_size - overlap)

        # Safety check to prevent infinite loops
        if start_token >= token_count:
            break

    print(f"[INFO] Text chunking completed - {len(chunks)} chunks generated")

    return chunks


def format_json_output(requirements: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Format the extraction results into the required JSON structure.

    Args:
        requirements: List of requirement dictionaries with extraction results

    Returns:
        Dict: Formatted JSON output
    """
    return {
        "extracted_data": requirements
    }
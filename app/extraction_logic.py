"""
Extraction logic module for processing BRD requirements and PDF chunks using GPT-4.
"""
import json
import concurrent.futures
import yaml
import time
import re
from typing import List, Dict, Any
import openai
from app.config import settings
from app.logger import log_execution, log_chunk_result, count_tokens
from difflib import Sequence<PERSON>atcher


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    Calculate similarity between two text strings using SequenceMatcher.

    Args:
        text1: First text string
        text2: Second text string

    Returns:
        float: Similarity ratio between 0.0 and 1.0
    """
    # Normalize texts: lowercase and remove extra whitespace
    text1 = re.sub(r'\s+', ' ', text1.lower()).strip()
    text2 = re.sub(r'\s+', ' ', text2.lower()).strip()

    # Use SequenceMatcher to calculate similarity
    return SequenceMatcher(None, text1, text2).ratio()

# Load prompts from YAML file
def load_prompts():
    """Load prompts from the YAML file."""
    with open("app/prompts.yaml", "r") as file:
        return yaml.safe_load(file)

# Global prompts dictionary
PROMPTS = load_prompts()


# OpenAI API settings are loaded from environment variables via settings

def identify_requirements(brd_text: str) -> List[Dict[str, Any]]:
    """
    Analyze the entire BRD and generate structured requirements.

    Args:
        brd_text: The text content of the BRD

    Returns:
        List[Dict[str, Any]]: List containing structured requirements from the BRD
    """
    # Set up OpenAI client
    client = openai.OpenAI(
        default_headers={"Content-Type": "application/json"}
    )
    log_execution("Analyzing BRD to extract structured requirements", "INFO")

    # Get the BRD analysis prompt from the YAML file and format it with the BRD text
    # The output schema is now included directly in the prompt
    prompt = PROMPTS["user_prompts"]["brd_analysis"].format(
        brd_text=brd_text
    )

    # Call GPT-4 API
    response = client.chat.completions.create(
        model=settings.openai_model,
        messages=[
            {"role": "system", "content": PROMPTS["system_prompts"]["brd_analysis"]},
            {"role": "user", "content": prompt}
        ],
        temperature=0.1,  # Low temperature for more deterministic output
        response_format={"type": "json_object"}
    )

    # Parse the response
    try:
        content = response.choices[0].message.content
        brd_analysis = json.loads(content)
        with open("brd_analysis.json", "w") as f:
            json.dump(brd_analysis, f, indent=2)

        # Log token usage
        prompt_tokens = count_tokens(prompt, settings.openai_model)
        response_tokens = count_tokens(content, settings.openai_model)
        log_execution(f"BRD analysis completed. Token usage: {prompt_tokens + response_tokens} total tokens ({prompt_tokens} prompt, {response_tokens} response)", "INFO")

        return [brd_analysis]

    except (json.JSONDecodeError, KeyError) as e:
        log_execution(f"Failed to parse BRD analysis: {str(e)}", "ERROR")
        log_execution(f"Raw response: {response.choices[0].message.content}", "ERROR")

        # Log token usage
        prompt_tokens = count_tokens(prompt, settings.openai_model)
        response_tokens = count_tokens(response.choices[0].message.content, settings.openai_model)
        log_execution(f"Token usage: {prompt_tokens + response_tokens} total tokens ({prompt_tokens} prompt, {response_tokens} response)", "INFO")

        raise ValueError(f"Failed to create BRD analysis: {str(e)}")


def process_chunk_for_requirement(requirement: Dict[str, Any], chunk: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a PDF chunk against the structured requirements using GPT-4.

    Args:
        requirement: Dictionary containing structured requirements information
        chunk: Dictionary containing PDF chunk text and source information

    Returns:
        Dict[str, Any]: Extraction results for this chunk
    """
    import time
    chunk_start_time = time.time()

    # Set up OpenAI client
    client = openai.OpenAI(
        default_headers={"Content-Type": "application/json"}
    )

    # Get the chunk text and source information
    chunk_text = chunk['text']
    source_info = f"{chunk['source_filename']}, Pages {', '.join(map(str, chunk.get('spanning_pages', [chunk['page_number']])))}."

    # Log the start of processing for this chunk
    chunk_id = f"{chunk['source_filename']}:p{chunk['page_number']}"
    log_execution(f"Starting processing chunk {chunk_id}", "DEBUG")

    # Get the extraction prompt from the YAML file and format it with the requirements, chunk text, and source info
    prompt = PROMPTS["user_prompts"]["extraction"].format(
        requirements_json=json.dumps(requirement, indent=2),
        chunk_text=chunk_text,
        source_info=source_info
    )

    # Call GPT-4 API with retry logic for rate limiting
    max_retries = 3
    retry_delay = 2  # seconds

    for retry in range(max_retries):
        try:
            api_call_start = time.time()
            response = client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": PROMPTS["system_prompts"]["extraction"]},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for more deterministic output
                response_format={"type": "json_object"}
            )
            api_call_duration = time.time() - api_call_start
            log_execution(f"API call for chunk {chunk_id} took {api_call_duration:.2f}s", "DEBUG")
            break  # Success, exit retry loop

        except Exception as e:
            if "rate limit" in str(e).lower() and retry < max_retries - 1:
                wait_time = retry_delay * (2 ** retry)  # Exponential backoff
                log_execution(f"Rate limit hit for chunk {chunk_id}, retrying in {wait_time}s (attempt {retry+1}/{max_retries})", "WARNING")
                time.sleep(wait_time)
            else:
                # If it's not a rate limit error or we've exhausted retries, re-raise
                log_execution(f"API call failed for chunk {chunk_id}: {str(e)}", "ERROR")
                raise

    # Log total processing time for this chunk
    chunk_duration = time.time() - chunk_start_time
    log_execution(f"Total processing time for chunk {chunk_id}: {chunk_duration:.2f}s", "DEBUG")

    # Parse the response
    try:
        content = response.choices[0].message.content
        if not content:
            print(f"[WARNING] Empty response from OpenAI API for chunk from {chunk['source_filename']}, Page {chunk['page_number']}")
            # Return a default "not relevant" result
            return {
                "is_relevant": False,
                "extracted_fields": [],
                "reasoning": "No response from API"
            }

        try:
            result = json.loads(content)
        except json.JSONDecodeError as json_err:
            print(f"[ERROR] JSON decode error: {str(json_err)}")
            print(f"[ERROR] Raw response: {content[:500]}...")  # Print first 500 chars to avoid flooding logs

            # Try to fix common JSON issues
            fixed_content = content

            # Fix unterminated strings
            if "Unterminated string" in str(json_err):
                # Add missing quotes at the end of the content if needed
                if content.count('"') % 2 != 0:
                    fixed_content = fixed_content + '"'

                # Replace problematic escape sequences
                fixed_content = fixed_content.replace('\\', '\\\\')

                # Try to parse again with fixed content
                try:
                    result = json.loads(fixed_content)
                    print("[INFO] Successfully fixed JSON and parsed it")
                except json.JSONDecodeError:
                    # If still failing, return a default result
                    print("[ERROR] Could not fix JSON, returning default result")
                    return {
                        "is_relevant": False,
                        "extracted_fields": [],
                        "reasoning": f"Invalid JSON response: {str(json_err)}"
                    }
            else:
                # For other JSON errors, return default result
                return {
                    "is_relevant": False,
                    "extracted_fields": [],
                    "reasoning": f"Invalid JSON response: {str(json_err)}"
                }

        # Add source information to the result
        if result.get("is_relevant", False):
            result["source_filename"] = chunk["source_filename"]
            result["page_number"] = chunk["page_number"]

            # Log which key points were addressed (if any)
            addressed_points = result.get("addressed_points", [])
            if addressed_points:
                log_execution(f"Chunk from {chunk['source_filename']}, Page {chunk['page_number']} addresses {len(addressed_points)} key points", "INFO")

        # Create source info dictionary for logging
        source_info_dict = {
            "source_filename": chunk["source_filename"],
            "page_number": chunk["page_number"],
            "spanning_pages": chunk.get("spanning_pages", [chunk["page_number"]])
        }

        # Log chunk result and token consumption
        token_stats = log_chunk_result(
            chunk_id=chunk_id,
            source_info=source_info_dict,
            prompt=prompt,
            response=content,
            result=result,
            model=settings.openai_model
        )

        # Add token stats to the result for internal tracking
        result["_token_stats"] = token_stats

        return result

    except Exception as e:
        log_execution(f"Unexpected error processing chunk: {str(e)}", "ERROR")
        log_execution(f"Error type: {type(e).__name__}", "ERROR")

        # Create a default result
        default_result = {
            "is_relevant": False,
            "extracted_fields": [],
            "reasoning": f"Error processing chunk: {str(e)}"
        }

        # Log the error in the results log
        source_info_dict = {
            "source_filename": chunk["source_filename"],
            "page_number": chunk["page_number"],
            "spanning_pages": chunk.get("spanning_pages", [chunk["page_number"]])
        }

        # Log with minimal token info since we don't have a valid response
        log_chunk_result(
            chunk_id=chunk_id,
            source_info=source_info_dict,
            prompt=prompt,
            response="ERROR: " + str(e),
            result=default_result,
            model=settings.openai_model
        )

        # Return a default result instead of raising an exception
        return default_result





def aggregate_results(requirement: Dict[str, Any], chunk_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Aggregate extraction results for the structured requirements across all processed chunks.

    Args:
        requirement: Dictionary containing structured requirements information
        chunk_results: List of extraction results for each chunk

    Returns:
        Dict[str, Any]: Aggregated results for the requirements
    """
    # Filter out non-relevant chunks
    relevant_results = [r for r in chunk_results if r.get("is_relevant", False)]
    log_execution(f"Aggregating results from {len(relevant_results)} relevant chunks out of {len(chunk_results)} total chunks", "INFO")

    # If no relevant chunks found
    if not relevant_results:
        # Get project info if available
        project_info = requirement.get("project_info", {})
        project_description = project_info.get("description", "")
        project_objective = project_info.get("objective", "")

        # Get data requirements if available
        data_requirements = requirement.get("data_requirements", [])

        return {
            "requirement_text": project_objective,
            "requirement_id": "BRD-ANALYSIS",
            "context": project_description,
            "key_points": [req.get("description", "") for req in data_requirements],
            "extracted_info": "",
            "supporting_evidence": {},
            "addressed_points": [],
            "notes": "No relevant information found in the provided documents.",
            "found": False
        }

    # If we have relevant chunks, aggregate the information
    # Collect all extracted fields from all relevant chunks
    all_extracted_fields = []
    for result in relevant_results:
        fields = result.get("extracted_fields", [])
        if fields:
            all_extracted_fields.extend(fields)

    # Group extracted fields by field_id
    grouped_fields = {}
    for field in all_extracted_fields:
        field_id = field.get("field_id", "unknown")
        if field_id not in grouped_fields:
            grouped_fields[field_id] = []
        grouped_fields[field_id].append(field)

    # For each field_id, select the field with highest confidence
    best_fields = {}
    for field_id, fields in grouped_fields.items():
        # Sort by confidence (high > medium > low)
        confidence_order = {"high": 3, "medium": 2, "low": 1}
        sorted_fields = sorted(
            fields,
            key=lambda f: confidence_order.get(f.get("confidence", "low"), 0),
            reverse=True
        )
        best_fields[field_id] = sorted_fields[0]

    # Create a comprehensive extracted info by combining all best fields
    extracted_info_parts = []
    for field_id, field in best_fields.items():
        field_name = field.get("field_name", field_id)
        extracted_value = field.get("extracted_value", "NOT_FOUND")
        confidence = field.get("confidence", "low")

        if extracted_value != "NOT_FOUND":
            extracted_info_parts.append(f"**{field_name}**: {extracted_value} (Confidence: {confidence})")

    combined_info = "\n\n".join(extracted_info_parts)

    # Collect all supporting evidence for each field
    evidence_by_field = {}

    for field in all_extracted_fields:
        field_id = field.get("field_id", "unknown")
        confidence = field.get("confidence", "low")
        confidence_score = {"high": 3, "medium": 2, "low": 1}.get(confidence, 0)

        if field.get("supporting_evidence"):
            if field_id not in evidence_by_field:
                evidence_by_field[field_id] = []

            # Add this evidence to the list for this field
            evidence_entry = {
                "text": field.get("supporting_evidence", ""),
                "source_filename": field.get("source_filename", ""),
                "page_number": field.get("page_number", ""),
                "confidence_score": confidence_score,
                "field_id": field_id
            }

            # Check for duplicates or near-duplicates using text similarity
            is_duplicate = False
            for existing_evidence in evidence_by_field[field_id]:
                # Exact match check
                if existing_evidence["text"] == evidence_entry["text"]:
                    is_duplicate = True
                    break

                # Near-duplicate check using similarity
                similarity = calculate_text_similarity(existing_evidence["text"], evidence_entry["text"])
                if similarity > 0.8:  # 80% similarity threshold
                    # If the new evidence has higher confidence, replace the existing one
                    if evidence_entry["confidence_score"] > existing_evidence["confidence_score"]:
                        # Replace the existing evidence with the higher confidence one
                        evidence_by_field[field_id].remove(existing_evidence)
                        evidence_by_field[field_id].append(evidence_entry)
                        log_execution(f"Replaced similar evidence with higher confidence version (similarity: {similarity:.2f})", "DEBUG")
                    is_duplicate = True
                    break

            if not is_duplicate:
                # Only add if we haven't reached the maximum number of evidences per field
                if len(evidence_by_field[field_id]) < 8:  # Limit to 8 evidences per field
                    evidence_by_field[field_id].append(evidence_entry)
                elif evidence_entry["confidence_score"] > min(e["confidence_score"] for e in evidence_by_field[field_id]):
                    # If we have reached the limit but this evidence has higher confidence than the lowest one
                    # Remove the lowest confidence evidence and add this one
                    lowest_conf_evidence = min(evidence_by_field[field_id], key=lambda e: e["confidence_score"])
                    evidence_by_field[field_id].remove(lowest_conf_evidence)
                    evidence_by_field[field_id].append(evidence_entry)
                    log_execution(f"Replaced lowest confidence evidence with higher confidence one", "DEBUG")

    # Find the best supporting evidence for each field (highest confidence)
    best_evidence_by_field = {}
    for field_id, evidence_list in evidence_by_field.items():
        # Sort by confidence score (high to low)
        sorted_evidence = sorted(evidence_list, key=lambda e: e["confidence_score"], reverse=True)
        best_evidence_by_field[field_id] = sorted_evidence[0]

    # Group evidence by field_id and sort by confidence
    grouped_evidence = {}
    for field_id, evidence_list in evidence_by_field.items():
        # Sort evidence by confidence score (high to low)
        sorted_evidence = sorted(evidence_list, key=lambda e: e["confidence_score"], reverse=True)

        # Create a list of evidence for this field without confidence scores
        grouped_evidence[field_id] = []
        for evidence in sorted_evidence:
            # Create a copy without the confidence_score
            evidence_copy = evidence.copy()
            if "confidence_score" in evidence_copy:
                del evidence_copy["confidence_score"]
            grouped_evidence[field_id].append(evidence_copy)

        # Log the number of evidences for this field
        log_execution(f"Field '{field_id}' has {len(grouped_evidence[field_id])} pieces of evidence after filtering", "INFO")

    # Create a flattened list for backward compatibility
    all_evidence = []
    for field_id, evidence_list in grouped_evidence.items():
        for evidence in evidence_list:
            all_evidence.append(evidence)

    # Sort all evidence by field_id for consistency
    all_evidence = sorted(all_evidence, key=lambda e: e["field_id"])

    # Get the best evidence overall (for backward compatibility)
    best_evidence = None
    if best_evidence_by_field:
        # Get the best evidence from any field
        all_best_field_evidence = list(best_evidence_by_field.values())
        # Sort by confidence score
        sorted_best_evidence = sorted(all_best_field_evidence, key=lambda e: e["confidence_score"], reverse=True)
        # Take the highest confidence one
        best_evidence = sorted_best_evidence[0].copy()
        # Remove the confidence_score
        if "confidence_score" in best_evidence:
            del best_evidence["confidence_score"]

    # If no best evidence found, use the first relevant result
    if not best_evidence and relevant_results:
        first_result = relevant_results[0]
        best_evidence = {
            "text": first_result.get("supporting_evidence", ""),
            "source_filename": first_result.get("source_filename", ""),
            "page_number": first_result.get("page_number", "")
        }

    # Get project info if available
    project_info = requirement.get("project_info", {})
    project_description = project_info.get("description", "")
    project_objective = project_info.get("objective", "")

    # Get data requirements if available
    data_requirements = requirement.get("data_requirements", [])

    # Create a list of key points from data requirements
    key_points = [req.get("description", "") for req in data_requirements]

    # Determine which key points were addressed and map them to their field_ids
    addressed_points = []
    point_to_field_id = {}  # Map from point description to field_id

    for field_id in best_fields.keys():
        # Find the corresponding requirement
        for req in data_requirements:
            if req.get("id") == field_id and best_fields[field_id].get("extracted_value") != "NOT_FOUND":
                point_description = req.get("description", "")
                addressed_points.append(point_description)
                point_to_field_id[point_description] = field_id

    # Create a mapping from addressed points to their evidence
    evidence_by_point = {}
    for point in addressed_points:
        field_id = point_to_field_id.get(point)
        if field_id and field_id in evidence_by_field:
            # Get all evidence for this field
            evidence_by_point[point] = evidence_by_field[field_id]

    # Log evidence mapping
    for point, evidence_list in evidence_by_point.items():
        log_execution(f"Point '{point[:30]}...' has {len(evidence_list)} pieces of evidence", "DEBUG")

    # Count how many key points were addressed
    addressed_count = len(addressed_points)

    # Collect detailed reasoning for each addressed point
    combined_reasoning = {}
    detailed_reasoning_by_field = {}

    # First, collect detailed reasoning from extracted fields
    for result in relevant_results:
        fields = result.get("extracted_fields", [])
        for field in fields:
            field_id = field.get("field_id", "unknown")
            detailed_reasoning = field.get("detailed_reasoning", "")
            extracted_value = field.get("extracted_value", "NOT_FOUND")

            # Skip if no detailed reasoning or if the field wasn't successfully extracted
            if not detailed_reasoning or extracted_value == "NOT_FOUND":
                continue

            # Find the corresponding requirement description
            field_description = ""
            for req in data_requirements:
                if req.get("id") == field_id:
                    field_description = req.get("description", "")
                    break

            if field_description:
                if field_description not in detailed_reasoning_by_field:
                    detailed_reasoning_by_field[field_description] = []

                # Add this detailed reasoning if it's not a duplicate and it's relevant
                if detailed_reasoning not in detailed_reasoning_by_field[field_description]:
                    detailed_reasoning_by_field[field_description].append(detailed_reasoning)

    # Then collect only relevant reasoning for each addressed point
    log_execution(f"Filtering reasoning for {len(addressed_points)} addressed points", "INFO")
    for point in addressed_points:
        # Start with any detailed reasoning we've collected
        if point in detailed_reasoning_by_field:
            # Only include reasoning that directly addresses the point
            relevant_reasoning = []
            total_reasoning_count = len(detailed_reasoning_by_field[point])

            # Extract key terms from the point (excluding common words)
            common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'of', 'from'}
            key_terms = [term.lower() for term in point.split() if term.lower() not in common_words][:5]

            log_execution(f"Point: '{point[:50]}...' - Found {total_reasoning_count} reasoning entries, key terms: {key_terms}", "DEBUG")

            for reasoning in detailed_reasoning_by_field[point]:
                # Check if the reasoning is actually relevant to this point
                # This helps filter out irrelevant chunks that might have been included
                if not reasoning:
                    continue

                # Check if any key terms appear in the reasoning
                reasoning_lower = reasoning.lower()
                is_relevant = False

                # If we have key terms, check if any appear in the reasoning and calculate a relevance score
                relevance_score = 0
                matching_terms = []

                if key_terms:
                    matching_terms = [term for term in key_terms if term in reasoning_lower]
                    # Calculate a relevance score based on how many key terms match
                    # and how frequently they appear in the reasoning
                    if matching_terms:
                        # Base score is the number of unique matching terms
                        relevance_score = len(matching_terms)

                        # Bonus for having a higher percentage of the key terms
                        term_coverage = len(matching_terms) / len(key_terms)
                        relevance_score += term_coverage * 2

                        # Bonus for terms appearing multiple times
                        for term in matching_terms:
                            # Count occurrences of the term
                            term_count = reasoning_lower.count(term)
                            if term_count > 1:
                                relevance_score += min(term_count / 2, 2)  # Cap the bonus at 2
                else:
                    # If no key terms (rare case), give a minimal score
                    relevance_score = 0.5

                # Consider it relevant if it has any score at all
                is_relevant = relevance_score > 0

                if is_relevant:
                    # Truncate reasoning if it's too long (for logging purposes)
                    reasoning_preview = reasoning[:100] + "..." if len(reasoning) > 100 else reasoning
                    log_execution(f"Including relevant reasoning (score: {relevance_score:.2f}): '{reasoning_preview}'", "DEBUG")
                    # Store the reasoning with its relevance score for later sorting
                    relevant_reasoning.append((reasoning, relevance_score, matching_terms))
                else:
                    # Log that we're skipping this reasoning
                    reasoning_preview = reasoning[:100] + "..." if len(reasoning) > 100 else reasoning
                    log_execution(f"Skipping irrelevant reasoning (score: {relevance_score:.2f}): '{reasoning_preview}'", "DEBUG")

            log_execution(f"Kept {len(relevant_reasoning)}/{total_reasoning_count} reasoning entries for point: '{point[:50]}...'", "INFO")

            if relevant_reasoning:
                # Sort the reasoning by relevance score (highest first)
                sorted_reasoning = sorted(relevant_reasoning, key=lambda x: x[1], reverse=True)

                # Log the top reasoning entries
                log_execution(f"Top reasoning entries for point '{point[:30]}...':", "DEBUG")
                for i, (reasoning_text, score, terms) in enumerate(sorted_reasoning[:3]):
                    preview = reasoning_text[:50] + "..." if len(reasoning_text) > 50 else reasoning_text
                    log_execution(f"  {i+1}. Score: {score:.2f}, Terms: {terms}, Text: '{preview}'", "DEBUG")

                # Format the reasoning with highlighting for matching terms
                formatted_reasoning = []

                # Limit to the top 3 most relevant reasoning entries to avoid overwhelming the user
                max_entries = 3
                top_reasoning = sorted_reasoning[:max_entries]

                for reasoning_text, score, matching_terms in top_reasoning:
                    # Add the reasoning text
                    formatted_reasoning.append(reasoning_text)

                # Join the formatted reasoning entries
                reasoning_text = ""
                if len(sorted_reasoning) > max_entries:
                    # Let the user know we're only showing the most relevant entries
                    reasoning_text = f"DETAILED ANALYSIS (showing top {max_entries} of {len(sorted_reasoning)} relevant entries):\n\n" + "\n\n".join(formatted_reasoning)
                else:
                    reasoning_text = "DETAILED ANALYSIS:\n\n" + "\n\n".join(formatted_reasoning)

                # Get evidence for this point
                point_evidence = evidence_by_point.get(point, [])

                # Create a combined structure with both reasoning and evidence
                combined_reasoning[point] = {
                    "reasoning_text": reasoning_text,
                    "evidence": point_evidence
                }

                log_execution(f"Point '{point[:30]}...' has {len(point_evidence)} pieces of evidence and reasoning text", "DEBUG")
            else:
                # No reasoning found, but might still have evidence
                point_evidence = evidence_by_point.get(point, [])

                combined_reasoning[point] = {
                    "reasoning_text": "No specific reasoning available for this point.",
                    "evidence": point_evidence
                }
        else:
            log_execution(f"No reasoning found for point: '{point[:50]}...'", "INFO")

            # No reasoning found, but might still have evidence
            point_evidence = evidence_by_point.get(point, [])

            combined_reasoning[point] = {
                "reasoning_text": "No specific reasoning available for this point.",
                "evidence": point_evidence
            }

        # We'll only use the detailed reasoning and skip the general reasoning
        # This removes the "Additional context" section

    log_execution(f"Addressed {addressed_count}/{len(key_points)} key points from the requirements", "INFO")
    log_execution(f"Collected reasoning for {len(combined_reasoning)} key points", "INFO")

    return {
        "requirement_text": project_objective,
        "requirement_id": "BRD-ANALYSIS",
        "context": project_description,
        "key_points": key_points,
        "extracted_info": combined_info,
        "supporting_evidence": best_evidence or {},
        "all_supporting_evidence": all_evidence or [],
        "grouped_evidence": grouped_evidence or {},  # Add the grouped evidence
        "addressed_points": addressed_points,
        "reasoning": combined_reasoning,
        "notes": f"Found relevant information in {len(relevant_results)} chunk(s). Addressed {addressed_count}/{len(key_points)} key points.",
        "found": True
    }


def process_chunks_in_parallel(requirement: Dict[str, Any], chunks: List[Dict[str, Any]],
                           max_workers: int = None) -> List[Dict[str, Any]]:
    """
    Process multiple chunks in parallel using a thread pool.

    Args:
        requirement: Dictionary containing structured requirements information
        chunks: List of dictionaries containing PDF chunk text and source information
        max_workers: Maximum number of worker threads to use (None = auto-determine based on CPU count)

    Returns:
        List[Dict[str, Any]]: List of extraction results for each chunk
    """
    # Import multiprocessing at the top level of the function
    import multiprocessing

    # If max_workers is not specified, use a reasonable default
    if max_workers is None:
        # Use the minimum of: (2 * CPU count) or (chunk count)
        cpu_count = multiprocessing.cpu_count()
        max_workers = min(2 * cpu_count, len(chunks))

    # For OpenAI API calls, we want to ensure we're not hitting rate limits
    # A good rule of thumb is to use at most 10 concurrent requests for most OpenAI models
    # But we'll respect the user's setting if provided
    if max_workers > 10 and settings.openai_model in ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"]:
        log_execution(f"Limiting max workers from {max_workers} to 10 to avoid OpenAI API rate limits", "WARNING")
        max_workers = 10

    # Ensure we have at least one worker
    max_workers = max(1, max_workers)

    # Get CPU count for logging
    cpu_count = multiprocessing.cpu_count()
    log_execution(f"Processing {len(chunks)} chunks in parallel using {max_workers} workers (system has {cpu_count} CPUs)", "INFO")

    # Create a thread pool executor
    results = []
    relevant_count = 0
    error_count = 0

    # Track start time for performance monitoring
    import time
    start_time = time.time()

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all chunk processing tasks
        future_to_chunk = {
            executor.submit(process_chunk_for_requirement, requirement, chunk): (i, chunk)
            for i, chunk in enumerate(chunks)
        }

        # Process results as they complete
        total_chunks = len(chunks)
        completed = 0

        for future in concurrent.futures.as_completed(future_to_chunk):
            chunk_idx, chunk = future_to_chunk[future]
            completed += 1

            try:
                # Get the result
                result = future.result()

                # Ensure source information is consistently added to the result
                if result.get("is_relevant", False):
                    # Make sure source information is included
                    if "source_filename" not in result:
                        result["source_filename"] = chunk.get("source_filename", "Unknown")

                    if "page_number" not in result:
                        result["page_number"] = chunk.get("page_number", "Unknown")

                    # Add spanning pages information if available
                    if "spanning_pages" in chunk and "spanning_pages" not in result:
                        result["spanning_pages"] = chunk["spanning_pages"]

                    relevant_count += 1

                    # Create page info string for logging
                    if 'page_numbers' in chunk and len(chunk['page_numbers']) > 1:
                        page_info = f"Pages {min(chunk['page_numbers'])}-{max(chunk['page_numbers'])}"
                    elif 'spanning_pages' in chunk and len(chunk['spanning_pages']) > 1:
                        page_info = f"Pages {', '.join(map(str, chunk['spanning_pages']))}"
                    else:
                        page_num = chunk.get('primary_page', chunk.get('page_number', 'Unknown'))
                        page_info = f"Page {page_num}"

                    log_execution(f"Relevant chunk from {chunk['source_filename']}, {page_info}", "INFO")

                results.append(result)

                # Log progress - only log periodically to avoid excessive output
                # This doesn't affect the actual parallel processing, just the logging
                log_interval = max(1, min(total_chunks // 10, 10))  # Log at most 10 times, at least once
                if completed % log_interval == 0 or completed == total_chunks:
                    elapsed_time = time.time() - start_time
                    remaining = (elapsed_time / completed) * (total_chunks - completed) if completed > 0 else 0
                    log_execution(f"Processed {completed}/{total_chunks} chunks ({completed/total_chunks*100:.1f}%) - "
                          f"Elapsed: {elapsed_time:.1f}s, Est. remaining: {remaining:.1f}s, Active workers: {len(executor._threads)}/{max_workers}", "INFO")

            except Exception as e:
                error_count += 1
                # Get source information for error reporting
                source_filename = chunk.get("source_filename", "Unknown")
                page_number = chunk.get("page_number", "Unknown")

                log_execution(f"Error processing chunk {chunk_idx} from {source_filename}, Page {page_number}: {str(e)}", "ERROR")
                import traceback
                log_execution(f"Traceback: {traceback.format_exc()}", "ERROR")

                # Create a default error result
                error_result = {
                    "is_relevant": False,
                    "extracted_fields": [],
                    "source_filename": source_filename,
                    "page_number": page_number,
                    "reasoning": f"Error processing chunk: {str(e)}"
                }

                # Log the error in the results log
                source_info_dict = {
                    "source_filename": source_filename,
                    "page_number": page_number,
                    "spanning_pages": chunk.get("spanning_pages", [page_number])
                }

                # Log with minimal token info since we don't have a valid response
                log_chunk_result(
                    chunk_id=f"{source_filename}:p{page_number}",
                    source_info=source_info_dict,
                    prompt="ERROR: Processing failed",
                    response="ERROR: " + str(e),
                    result=error_result,
                    model=settings.openai_model
                )

                # Add the error result to maintain the correct count
                results.append(error_result)

    # Calculate processing statistics
    elapsed_time = time.time() - start_time
    chunks_per_second = total_chunks / elapsed_time if elapsed_time > 0 else 0

    # Calculate token statistics
    total_prompt_tokens = 0
    total_response_tokens = 0
    total_tokens = 0

    for result in results:
        if "_token_stats" in result:
            total_prompt_tokens += result["_token_stats"].get("prompt_tokens", 0)
            total_response_tokens += result["_token_stats"].get("response_tokens", 0)
            total_tokens += result["_token_stats"].get("total_tokens", 0)

            # Remove the token stats from the result to keep it clean
            del result["_token_stats"]

    token_stats = {
        "prompt_tokens": total_prompt_tokens,
        "response_tokens": total_response_tokens,
        "total_tokens": total_tokens,
        "avg_tokens_per_chunk": total_tokens / total_chunks if total_chunks > 0 else 0
    }

    # Log summary
    log_execution(f"Completed parallel processing of {len(chunks)} chunks in {elapsed_time:.2f} seconds "
          f"({chunks_per_second:.2f} chunks/sec)", "INFO")
    log_execution(f"Found {relevant_count} relevant chunks, {error_count} errors", "INFO")
    log_execution(f"Token usage: {total_tokens} total tokens ({total_prompt_tokens} prompt, {total_response_tokens} response)", "INFO")

    # Add a process ID for the summary log
    import uuid
    process_id = str(uuid.uuid4())[:8]

    # Import the log_summary function
    from app.logger import log_summary

    # Log detailed summary to both log files
    log_summary(
        process_id=process_id,
        total_chunks=total_chunks,
        relevant_chunks=relevant_count,
        token_stats=token_stats,
        elapsed_time=elapsed_time
    )

    return results

# Prompts for the BRD-Driven PDF Information Extractor

# System prompts
system_prompts:
  brd_analysis: >
    You are an expert business analyst who can analyze complex Business Requirements Documents (BRDs)
    and extract all key points, requirements, and essential information contained within them.

  extraction: >
    You are an expert medical document analyst who extracts specific clinical information from documents
    based on BRD analysis. You provide detailed reasoning for how evidence supports key points,
    including specific values, measurements, and clinical documentation elements that satisfy requirements.

# User prompts
user_prompts:
  brd_analysis: >
    You are an expert in clinical requirements analysis and structuring medical validation criteria from Business Requirements Documents (BRDs) into a machine-readable format suitable for driving a validation engine and user interface.

    Your task is to analyze the provided medical condition validation BRD, specifically focusing on the criteria, data points, business logic, and UI requirements for validating the described condition.

    Instructions:
    1.  Carefully analyze the provided BRD. Identify:
        *   The medical condition being validated.
        *   The specific *documentation requirements* (types, sources, keywords).
        *   The specific *clinical evidence requirements*, including:
            *   Required lab parameters or clinical measurements.
            *   How these measurements are evaluated (e.g., lowest/highest value during stay).
            *   How reference ranges or specific thresholds are used (e.g., facility-specific vs. universal).
            *   Any required calculations (e.g., ANC calculation).
            *   Conditions that result in an 'uncertain' status for a criterion.
        *   The *overall decision logic* that combines the documentation and clinical evidence criteria to determine the final validation status (e.g., "all criteria must be met").
        *   Any *supplemental information* required for context or display (e.g., other diagnoses, past medical history, medications, demographics used in logic like age/sex).
        *   Specific *User Interface requirements*, including field ordering, grouping, display formats (e.g., grids, lists), handling of calculated values, and visual status indicators (e.g., green for met, red for not met, brown for uncertain).

    2.  Extract ALL requirements, necessary data fields, validation rules derived from the clinical logic, and business logic.

    3.  Organize these requirements into the structured JSON format following this schema:

        {{
            "project_info": {{
                "title": "Medical Condition Validation Title from BRD",
                "description": "Brief description of the validation project and the medical condition.",
                "objective": "Main objective of the validation process."
            }},
            "data_requirements": [
                {{
                    "id": "unique_id_for_requirement_or_data_point",
                    "name": "Human-readable name for this data point or criterion status",
                    "description": "Detailed description of this data point or what this criterion represents, referencing the BRD.",
                    "type": "Determine the best fit: text, number, date, select (for discrete options like Met/Not Met/Uncertain, Adult/Pediatric, Male/Female), multiselect, checkbox (for boolean presence), file, table (could represent lab value grids).",
                    "required": true/false,
                    "validation": {{
                        "rules": ["List of input validation rules if mentioned for capturing data"],
                        "min": minimum value (for number type),
                        "max": maximum value (for number type),
                        "pattern": "regex pattern if applicable",
                        "options": ["List of options for select/multiselect types"]
                    }},
                    "dependencies": [
                        {{
                            "field_id": "ID of field this depends on (e.g., Calculated ANC depends on WBC and Band count)",
                            "condition": "Condition that must be met (e.g., 'is not documented')",
                            "value": "Value that triggers this dependency (e.g., true or specific value)"
                        }}
                    ],
                    "ui_settings": {{
                        "group": "Logical grouping for UI organization (e.g., 'Documentation', 'Clinical Evidence - Red Cells', 'Supplemental')",
                        "order": numeric order for display within the group (follow BRD section 3.3.1 where applicable),
                        "width": "full", "half", or specific percentage,
                        "help_text": "Help text to display to users, potentially explaining the criterion or data point."
                    }}
                }}
            ],
            "business_rules": [
                {{
                    "id": "rule_id",
                    "description": "Description of business rule (e.g., 'Determine Red Cells Criterion Status', 'Calculate ANC', 'Determine Final Pancytopenia Status')",
                    "condition": "Condition that triggers this rule or defines the logic (e.g., 'IF Lowest Hb < Hb Ref Range Lower Limit OR Lowest Hct < Hct Ref Range Lower Limit OR Lowest RBC < RBC Ref Range Lower Limit')",
                    "action": "Action to take when condition is met or logic is applied (e.g., 'THEN Set 'Red Cells Criterion Status' to 'Met'')",
                    "triggers": ["List of field IDs or events that trigger evaluation of this rule"]
                }}
            ],
            "output_formats": [
                {{
                    "name": "Name of output format (e.g., 'UI Display')",
                    "description": "Description of this output format",
                    "fields": ["List of field IDs to include in this output, following the UI ordering/grouping"]
                }}
            ]
        }}

    4.  For each data point in `data_requirements`, determine the most appropriate `type` based on the data it holds (e.g., use 'select' for Met/Not Met/Uncertain statuses, 'list' or 'multiselect' for lists of conditions/meds, 'number' for lab values/ranges).

    5.  Translate the clinical validation logic (thresholds, ranges, calculations, combinations) into explicit `business_rules` entries, clearly defining the conditions and the resulting status/value changes. Pay close attention to the use of "lowest value for stay" and the handling of facility-specific vs. universal reference ranges.

    6.  Identify dependencies between data points or rules (e.g., the final status depends on all criteria statuses).

    7.  Use the `ui_settings` within `data_requirements` to capture logical grouping and display order specified in the BRD (especially section 3.3.1). Add notes about specific display requirements (like grid format, handling calculated values, status color mapping) either in the description or potentially in custom keys within `ui_settings` if the schema were extendable, or note them explicitly in the `output_formats` description. For this prompt, focus on mapping group and order primarily, and note complex UI logic in the `business_rules` or descriptions where applicable.

    8.  Ensure the `output_formats` section reflects the fields required for the primary UI display described in the BRD.

    9.  Include relevant terms and abbreviations from the Definitions section in descriptions for clarity.

    BRD DOCUMENT:
    {brd_text}

    Return ONLY a valid JSON object following the schema above, containing all the structured requirements extracted from the BRD.

  extraction: >
    You are an intelligent document analysis assistant. Your task is to extract specific information from a PDF document based on the structured requirements provided.

    Instructions:
    1. Use ONLY the provided requirements to guide your extraction
    2. Thoroughly examine the PDF chunk and extract ALL information that matches the data requirements
    3. Present the extracted information in a structured JSON format that aligns with the requirements
    4. For each data requirement, extract the corresponding value if found in the PDF chunk
    5. If any requested information is not found in this chunk, mark it as "NOT_FOUND"
    6. ALWAYS include supporting evidence for EVERY extracted field - this is CRITICAL
    7. The supporting evidence must be the EXACT text from the document that supports your extraction
    8. The supporting evidence should include enough context to understand why this text supports the extraction
    9. For each extracted field, you MUST provide:
       - The extracted value
       - The supporting evidence (exact text from the document)
       - The confidence level (high, medium, low)
       - The page number where it was found
    10. IMPORTANT: The chunk may contain page markers in the format "===== PAGE X =====". Use these markers to determine the correct page number for each piece of evidence.

    NOTE -  1. Supporting evidence must be the whole sentences from the medical record without paraphrasing or revising.
            2. If there are multiple Supporting Sentences, use this format: "sent_1", "sent_2"
            3. If there is NO supporting sentence, then return UNKNOWN instead.

    DETAILED REASONING REQUIREMENTS:
    1. For each extracted field, provide SPECIFIC and DETAILED reasoning about what you identified in the PDF
    2. Your reasoning must explain EXACTLY what clinical evidence, values, or documentation you found that satisfies the criteria
    3. Include specific values, measurements, or documentation elements that support your decision
    4. Explain WHY the evidence you found meets the specific requirement criteria
    5. If you found multiple pieces of evidence, explain how they work together to support the requirement
    6. If the evidence is partial or ambiguous, explain what is missing or unclear
    7. Use medical terminology appropriately when explaining your reasoning
    8. Format your reasoning as a clear, concise analysis that a medical coder would understand

    STRUCTURED REQUIREMENTS:
    {requirements_json}

    PDF CHUNK:
    {chunk_text}

    SOURCE: {source_info}

    Return a JSON object with the following structure:
    {{
        "is_relevant": true/false,
        "extracted_fields": [
            {{
                "field_id": "ID of the field from requirements",
                "field_name": "Name of the field from requirements",
                "extracted_value": "Value extracted from the PDF",
                "supporting_evidence": "Exact text from the PDF that supports this extraction",
                "confidence": "high/medium/low",
                "page_number": "The page number where this evidence was found (use page markers to determine)",
                "detailed_reasoning": "Detailed explanation of exactly what you identified in the PDF that satisfies this specific requirement, including specific values, measurements, or documentation elements"
            }}
        ],
        "reasoning": "Comprehensive explanation of why this chunk is relevant or not relevant, with specific references to the evidence found"
    }}

"""
Logging module for the application.

This module provides logging functionality for the application, including:
1. Execution logs and progress updates
2. Chunk results and token consumption tracking
"""

import os
import logging
import json
from datetime import datetime  # Used for log entry timestamps
from typing import Dict, Any
import tiktoken
import ssl
import urllib.request

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Maximum log file size in bytes (default: 10MB)
MAX_LOG_SIZE = int(os.environ.get('MAX_LOG_SIZE', 10 * 1024 * 1024))

# Check if SSL verification should be disabled (for environments with SSL issues)
DISABLE_SSL_VERIFICATION = os.environ.get('DISABLE_SSL_VERIFICATION', 'false').lower() == 'true'

# Function to rotate log file if it gets too large
def rotate_log_file(log_file_path):
    """
    Rotate a log file if it exceeds the maximum size.

    Args:
        log_file_path: Path to the log file to check
    """
    try:
        # Check if the file exists and is too large
        if os.path.exists(log_file_path) and os.path.getsize(log_file_path) > MAX_LOG_SIZE:
            # Create a backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{log_file_path}.{timestamp}"

            # Rename the current log file to the backup name
            os.rename(log_file_path, backup_path)

            # Log the rotation (to console only to avoid recursive logging)
            print(f"Log file {log_file_path} rotated to {backup_path}")
    except Exception as e:
        # Log any errors to console only
        print(f"Error rotating log file {log_file_path}: {str(e)}")

# Rotate logs if needed
rotate_log_file(os.path.join('logs', 'execution.log'))
rotate_log_file(os.path.join('logs', 'results.log'))

# If SSL verification is disabled, create an unverified SSL context
if DISABLE_SSL_VERIFICATION:
    # Create an unverified SSL context
    ssl_context = ssl._create_unverified_context()
    # Install the SSL context for urllib.request
    urllib.request.urlopen = lambda url, *args, **kwargs: urllib.request.urlopen(
        url, *args, context=ssl_context, **kwargs
    )
    logging.warning("SSL certificate verification has been disabled. This is not recommended for production environments.")

# Configure the execution logger
execution_logger = logging.getLogger('execution_logger')
execution_logger.setLevel(logging.INFO)

# Create a file handler for the execution log
execution_log_file = os.path.join('logs', 'execution.log')
execution_handler = logging.FileHandler(execution_log_file, mode='a')  # 'a' for append mode
execution_handler.setLevel(logging.INFO)

# Create a formatter for the execution log
execution_formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] %(message)s')
execution_handler.setFormatter(execution_formatter)

# Add the handler to the execution logger
execution_logger.addHandler(execution_handler)

# Configure the results logger
results_logger = logging.getLogger('results_logger')
results_logger.setLevel(logging.INFO)

# Create a file handler for the results log
results_log_file = os.path.join('logs', 'results.log')
results_handler = logging.FileHandler(results_log_file, mode='a')  # 'a' for append mode
results_handler.setLevel(logging.INFO)

# Create a formatter for the results log
results_formatter = logging.Formatter('[%(asctime)s] %(message)s')
results_handler.setFormatter(results_formatter)

# Add the handler to the results logger
results_logger.addHandler(results_handler)

# Add console handler for both loggers
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] %(message)s')
console_handler.setFormatter(console_formatter)
execution_logger.addHandler(console_handler)

# Simple token counter that doesn't rely on tiktoken
def simple_token_count(text: str) -> int:
    """
    A simple token counter that approximates tokens without using tiktoken.
    This is used as a fallback when tiktoken fails.

    Args:
        text: The text to count tokens for

    Returns:
        int: Approximate number of tokens
    """
    # Split by whitespace for a rough count
    words = text.split()
    # Count punctuation and special characters
    punctuation = sum(1 for c in text if c in '.,;:!?()[]{}"\'`~@#$%^&*-+=|\\/<>')
    # Estimate: each word is roughly 1.3 tokens, and each punctuation is 0.5 tokens
    return int(len(words) * 1.3 + punctuation * 0.5)

# Token counter
def count_tokens(text: str, model: str = "gpt-4") -> int:
    """
    Count the number of tokens in a text string.

    Args:
        text: The text to count tokens for
        model: The model to use for counting tokens

    Returns:
        int: The number of tokens in the text
    """
    # If text is empty, return 0
    if not text:
        return 0

    try:
        # First try with cl100k_base encoding which is used by many models and is often cached
        try:
            encoding = tiktoken.get_encoding("cl100k_base")
            return len(encoding.encode(text))
        except Exception as inner_e:
            # If that fails, try the model-specific encoding
            try:
                encoding = tiktoken.encoding_for_model(model)
                return len(encoding.encode(text))
            except Exception as model_e:
                execution_logger.warning(f"Model-specific encoding failed: {str(model_e)}")
                raise inner_e
    except Exception as e:
        execution_logger.warning(f"Error counting tokens with tiktoken: {str(e)}")
        # Use our simple token counter as fallback
        token_count = simple_token_count(text)
        execution_logger.info(f"Using simple token counter as fallback. Estimated {token_count} tokens.")
        return token_count

# Log execution information
def log_execution(message: str, level: str = "INFO") -> None:
    """
    Log execution information.

    Args:
        message: The message to log
        level: The log level (INFO, WARNING, ERROR, DEBUG)
    """
    if level == "INFO":
        execution_logger.info(message)
    elif level == "WARNING":
        execution_logger.warning(message)
    elif level == "ERROR":
        execution_logger.error(message)
    elif level == "DEBUG":
        execution_logger.debug(message)

# Log chunk results
def log_chunk_result(chunk_id: str, source_info: Dict[str, Any],
                     prompt: str, response: str, result: Dict[str, Any],
                     model: str = "gpt-4") -> Dict[str, Any]:
    """
    Log chunk processing results and token consumption.

    Args:
        chunk_id: Identifier for the chunk
        source_info: Source information for the chunk
        prompt: The prompt sent to the LLM
        response: The raw response from the LLM
        result: The processed result
        model: The model used for processing

    Returns:
        Dict[str, Any]: Token consumption statistics
    """
    # Count tokens
    prompt_tokens = count_tokens(prompt, model)
    response_tokens = count_tokens(response, model)
    total_tokens = prompt_tokens + response_tokens

    # Create token stats
    token_stats = {
        "prompt_tokens": prompt_tokens,
        "response_tokens": response_tokens,
        "total_tokens": total_tokens
    }

    # Create log entry
    log_entry = {
        "chunk_id": chunk_id,
        "source_info": source_info,
        "token_stats": token_stats,
        "is_relevant": result.get("is_relevant", False),
        "extracted_fields_count": len(result.get("extracted_fields", [])),
        "result": result
    }

    # Log the entry
    # results_logger.info(json.dumps(log_entry))

    return token_stats

# Log summary statistics
def log_summary(process_id: str, total_chunks: int, relevant_chunks: int,
                token_stats: Dict[str, int], elapsed_time: float) -> None:
    """
    Log summary statistics for a processing run.

    Args:
        process_id: The process ID
        total_chunks: Total number of chunks processed
        relevant_chunks: Number of relevant chunks found
        token_stats: Token consumption statistics
        elapsed_time: Total elapsed time in seconds
    """
    summary = {
        "process_id": process_id,
        "total_chunks": total_chunks,
        "relevant_chunks": relevant_chunks,
        "token_stats": token_stats,
        "elapsed_time_seconds": elapsed_time,
        "chunks_per_second": total_chunks / elapsed_time if elapsed_time > 0 else 0
    }

    execution_logger.info(f"Processing Summary: {json.dumps(summary)}")
    results_logger.info(f"SUMMARY: {json.dumps(summary)}")

# Get log file paths
def get_log_files() -> Dict[str, str]:
    """
    Get the paths to the log files.

    Returns:
        Dict[str, str]: Dictionary with log file paths
    """
    return {
        "execution_log": execution_log_file,
        "results_log": results_log_file
    }

# Add a session separator to the logs
def add_session_separator():
    """
    Add a session separator to both log files to clearly mark the start of a new session.
    """
    separator = f"\n{'='*80}\n SESSION STARTED AT {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} {'='*80}\n"
    log_execution(separator, "INFO")
    results_logger.info(separator)

# Add the session separator when the module is imported
add_session_separator()

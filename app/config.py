"""
Configuration settings for the PDF Information Extractor.
"""
import os
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings."""

    # OpenAI API settings
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_api_base: str = os.getenv("OPENAI_API_BASE", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "")

    # Processing settings
    chunk_size: int = int(os.getenv("CHUNK_SIZE", "4000"))  # Characters per chunk (will be converted to tokens)
    chunk_overlap: int = int(os.getenv("CHUNK_OVERLAP", "200"))  # Overlap between chunks (will be converted to tokens)
    max_parallel_workers: int = int(os.getenv("MAX_PARALLEL_WORKERS", "10"))  # Maximum number of parallel workers for chunk processing

    # API settings
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)

    # File upload settings
    max_upload_size: int = Field(default=50 * 1024 * 1024)  # 50 MB
    allowed_brd_extensions: list = Field(default=[".doc", ".docx"])
    allowed_pdf_extensions: list = Field(default=[".pdf",".pkl"])

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore"
    }


settings = Settings()
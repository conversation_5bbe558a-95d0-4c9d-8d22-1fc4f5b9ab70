"""
Document processing module for extracting text from BRD and PDF files.
"""
import os
from typing import List, Dict, Any, <PERSON>ple
import docx
import PyPDF2
from app.utils import chunk_text_data
from app.config import settings


def extract_text_from_docx(file_path: str) -> str:
    """
    Extract text content from a DOCX file.

    Args:
        file_path: Path to the DOCX file

    Returns:
        str: Extracted text content
    """
    doc = docx.Document(file_path)
    full_text = []

    for para in doc.paragraphs:
        full_text.append(para.text)

    return '\n'.join(full_text)


def extract_text_from_doc(file_path: str) -> str:
    """
    Extract text content from a DOC file.

    Note: This is a placeholder. For actual DOC files, you might need
    additional libraries like textract or antiword.

    Args:
        file_path: Path to the DOC file

    Returns:
        str: Extracted text content
    """
    # For simplicity, we're treating DOC files like DOCX
    # In a production environment, you'd want to use a proper DOC parser
    try:
        return extract_text_from_docx(file_path)
    except:
        raise ValueError(f"Could not extract text from DOC file: {file_path}")


def extract_text_from_brd(file_path: str) -> str:
    """
    Extract text from a BRD file (DOC or DOCX).

    Args:
        file_path: Path to the BRD file

    Returns:
        str: Extracted text content
    """
    _, ext = os.path.splitext(file_path.lower())

    if ext == '.docx':
        return extract_text_from_docx(file_path)
    elif ext == '.doc':
        return extract_text_from_doc(file_path)
    else:
        raise ValueError(f"Unsupported BRD file format: {ext}")


def extract_text_from_pdf(file_path: str) -> List[Dict[str, Any]]:
    """
    Extract text from a PDF file, preserving page numbers.

    Args:
        file_path: Path to the PDF file

    Returns:
        List[Dict[str, Any]]: List of dictionaries with page number and text content
    """
    result = []
    filename = os.path.basename(file_path)

    # This code is no longer used - we now use the more comprehensive pickle checking in the updated function
    # Keeping this for backward compatibility with any existing code that might still use it
    filename_without_ext = os.path.splitext(filename)[0]
    pkl_path = os.path.join('pickles', f"{filename_without_ext}.pkl")
    if os.path.exists(pkl_path):
        import pickle
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)
        for page_wise in data['page']:
            result.append({
                'source_filename': filename,
                'page_number': page_wise['metadata']['page'],
                'text': page_wise['text']
            })
        print("[INFO] Text extraction from PDF completed")
        return result

    if ".pkl" in filename:
        import pickle
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        for page_wise in data['page']:
            result.append({
                'source_filename': filename,
                'page_number': page_wise['metadata']['page'],
                'text': page_wise['text']
            })
        print("[INFO] Text extraction from PDF completed")
        return result

    with open(file_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        print("[INFO] Extracting text from PDF")

        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text = page.extract_text()

            if text.strip():  # Only add non-empty pages
                result.append({
                    'source_filename': filename,
                    'page_number': page_num + 1,  # 1-based page numbering
                    'text': text
                })
        print("[INFO] Text extraction from PDF completed")

    return result


def chunk_pdf_text(pdf_pages: List[Dict[str, Any]], chunk_size: int = None,
                  chunk_overlap: int = None) -> List[Dict[str, Any]]:
    """
    Split PDF text into chunks based on token count while preserving source information.
    First combines all pages from the same file, then chunks the combined text.
    Adds page separators to track page numbers.

    Args:
        pdf_pages: List of dictionaries with page number and text content
        chunk_size: Maximum number of tokens per chunk (default from settings)
        chunk_overlap: Number of tokens to overlap (default from settings)

    Returns:
        List[Dict[str, Any]]: List of dictionaries with source info and text chunks
    """
    # Use the chunk size from settings if not provided
    if chunk_size is None:
        chunk_size = settings.chunk_size
        print(f"[INFO] Using chunk size of {chunk_size} tokens")

    if chunk_overlap is None:

        chunk_overlap = settings.chunk_overlap
        print(f"[INFO] Using chunk overlap of {chunk_overlap} tokens ")

    chunks = []
    print("[INFO] Chunking PDF text - combining pages first")

    # Group pages by filename
    files_dict = {}
    for page in pdf_pages:
        filename = page['source_filename']
        if filename not in files_dict:
            files_dict[filename] = []
        files_dict[filename].append(page)

    # Sort pages by page number for each file
    for filename, pages in files_dict.items():
        pages.sort(key=lambda x: x['page_number'])

    # Process each file
    for filename, pages in files_dict.items():
        print(f"[INFO] Processing file: {filename} with {len(pages)} pages")

        # Combine all text from the file with page markers
        combined_text = ""
        page_markers = {}  # Dictionary mapping character positions to page numbers

        for page in pages:
            page_start = len(combined_text)
            page_text = page['text']

            # Add a page marker at the start position
            page_markers[page_start] = page['page_number']

            # Add the page text
            combined_text += page_text

            # Add a newline between pages if not already present
            if not combined_text.endswith('\n'):
                combined_text += '\n'

        print(f"[DETAILS] Combined text length: {len(combined_text)} characters")

        # Chunk the combined text with token-based chunking and page markers
        chunk_dicts = chunk_text_data(combined_text, chunk_size, chunk_overlap, page_markers)
        print(f"[DETAILS] Generated {len(chunk_dicts)} chunks from combined text")

        # Create the final chunks with source information
        for i, chunk_dict in enumerate(chunk_dicts):
            # Get the pages this chunk spans
            spanning_pages = chunk_dict['pages']

            # If no pages found (shouldn't happen), use the first page
            if not spanning_pages:
                spanning_pages = [pages[0]['page_number']]

            # Create the chunk with source information
            chunks.append({
                'source_filename': filename,
                'page_number': spanning_pages[0] if spanning_pages else 1,  # Primary page (first one)
                'spanning_pages': spanning_pages,  # All pages this chunk spans
                'chunk_index': i,
                'text': chunk_dict['text'],
                'page_map': chunk_dict['page_map']  # Map of page numbers to text segments
            })

    print(f"[INFO] PDF text chunking completed - {len(chunks)} chunks generated")
    return chunks
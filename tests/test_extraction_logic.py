"""
Tests for the extraction logic module.
"""
import unittest
from unittest.mock import patch, MagicMock

from app.extraction_logic import (
    aggregate_results
)


class TestExtractionLogic(unittest.TestCase):
    """Test cases for extraction logic functions."""

    def test_aggregate_results_with_relevant_chunks(self):
        """Test aggregating results with relevant chunks."""
        # Sample structured requirement
        requirement = {
            "project_info": {
                "title": "PDF Extraction System",
                "description": "A system for extracting information from PDFs",
                "objective": "The system shall support PDF extraction."
            },
            "data_requirements": [
                {
                    "id": "req-001",
                    "name": "PDF Extraction Support",
                    "description": "The system must support PDF extraction capabilities",
                    "type": "text"
                },
                {
                    "id": "req-002",
                    "name": "Metadata Extraction",
                    "description": "The system should extract metadata from PDFs",
                    "type": "text"
                }
            ]
        }

        # Sample chunk results with relevant information in the new format
        chunk_results = [
            {
                "is_relevant": True,
                "extracted_fields": [
                    {
                        "field_id": "req-001",
                        "field_name": "PDF Extraction Support",
                        "extracted_value": "The system supports PDF extraction through PyPDF2.",
                        "supporting_evidence": "PDF extraction is implemented using PyPDF2 library.",
                        "confidence": "high",
                        "page_number": 5
                    }
                ],
                "source_filename": "document1.pdf",
                "page_number": 5,
                "reasoning": "The document clearly states that PDF extraction is supported."
            },
            {
                "is_relevant": True,
                "extracted_fields": [
                    {
                        "field_id": "req-002",
                        "field_name": "Metadata Extraction",
                        "extracted_value": "PDF extraction capabilities include text and metadata.",
                        "supporting_evidence": "The extraction module can process both text and metadata.",
                        "confidence": "medium",
                        "page_number": 12
                    }
                ],
                "source_filename": "document2.pdf",
                "page_number": 12,
                "reasoning": "The document mentions metadata extraction capabilities."
            },
            {
                "is_relevant": False
            }
        ]

        # Call the function
        result = aggregate_results(requirement, chunk_results)

        # Assertions
        self.assertEqual(result["requirement_text"], requirement["project_info"]["objective"])
        self.assertTrue(result["found"])
        self.assertIn("supporting_evidence", result)
        self.assertTrue(len(result["extracted_info"]) > 0)
        self.assertIn("PDF Extraction Support", result["extracted_info"])
        self.assertIn("Metadata Extraction", result["extracted_info"])
        self.assertIn("Found relevant information", result["notes"])

    def test_aggregate_results_with_no_relevant_chunks(self):
        """Test aggregating results with no relevant chunks."""
        # Sample structured requirement
        requirement = {
            "project_info": {
                "title": "Image Extraction System",
                "description": "A system for extracting information from images",
                "objective": "The system shall support image extraction."
            },
            "data_requirements": [
                {
                    "id": "req-001",
                    "name": "Image Extraction Support",
                    "description": "The system must support image extraction capabilities",
                    "type": "text"
                },
                {
                    "id": "req-002",
                    "name": "OCR Support",
                    "description": "The system should support OCR for images",
                    "type": "text"
                }
            ]
        }

        # Sample chunk results with no relevant information
        chunk_results = [
            {
                "is_relevant": False
            },
            {
                "is_relevant": False
            }
        ]

        # Call the function
        result = aggregate_results(requirement, chunk_results)

        # Assertions
        self.assertEqual(result["requirement_text"], requirement["project_info"]["objective"])
        self.assertFalse(result["found"])
        self.assertEqual(result["extracted_info"], "")
        self.assertEqual(result["supporting_evidence"], {})
        self.assertIn("No relevant information found", result["notes"])

        # Check that key points were extracted from data requirements
        key_points = [req["description"] for req in requirement["data_requirements"]]
        for point in key_points:
            self.assertIn(point, result["key_points"])


if __name__ == '__main__':
    unittest.main()
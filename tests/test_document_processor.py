"""
Tests for the document processor module.
"""
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock

from app.document_processor import (
    extract_text_from_docx,
    extract_text_from_brd,
    extract_text_from_pdf,
    chunk_pdf_text
)


class TestDocumentProcessor(unittest.TestCase):
    """Test cases for document processor functions."""

    def test_chunk_pdf_text(self):
        """Test chunking PDF text."""
        # Sample PDF pages
        pdf_pages = [
            {
                'source_filename': 'test.pdf',
                'page_number': 1,
                'text': 'This is a test page with some content. ' * 20
            },
            {
                'source_filename': 'test.pdf',
                'page_number': 2,
                'text': 'This is another test page with different content. ' * 20
            }
        ]

        # Test with custom chunk size and overlap
        chunks = chunk_pdf_text(pdf_pages, chunk_size=100, chunk_overlap=10)

        # Assertions
        self.assertIsInstance(chunks, list)
        self.assertTrue(len(chunks) > 0)

        # Check chunk structure
        for chunk in chunks:
            self.assertIn('source_filename', chunk)
            self.assertIn('page_number', chunk)
            self.assertIn('chunk_index', chunk)
            self.assertIn('text', chunk)

            # Check source information is preserved
            self.assertEqual(chunk['source_filename'], 'test.pdf')
            self.assertIn(chunk['page_number'], [1, 2])

            # Check chunk size
            self.assertLessEqual(len(chunk['text']), 100)


if __name__ == '__main__':
    unittest.main()
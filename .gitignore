# Byte-compiled / optimized / DLL files
__pycache__/*
*.py[cod]

# Environment variables
.env
# Distribution / packaging
bin/
build/
develop-eggs/
dist/
eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
*.xmls
*.iml
*.idea
app/__pycache__/*

# Installer logs
pip-log.txt

# Data folder
uploads/*
BRD/*
DATA/*
*.DS_Store

# Unit test / coverage reports
.cache
frontend/uploaded_data/*
uploaded_data/*``
app/__pycache__/utils.cpython-311.pyc
app/__pycache__/extraction_logic.cpython-311.pyc
app/__pycache__/*
app/__pycache__/main.cpython-311.pyc
uploaded_data/*
frontend/uploaded_data/*
logs/*

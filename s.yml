---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ica-engine-execute-prompts
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: ica-engine-execute-prompts
      annotations:
        vault.hashicorp.com/agent-cache-enable: "true"
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-inject-secret-creds: dig-timber-genai/prod/data
        vault.hashicorp.com/agent-inject-template-creds: >
          {{- with secret "dig-timber-genai/prod/data/protegrity" -}}
          [default]

          open_api_key  = {{ .Data.data.open_api_key }}

          {{- end }}
          {{- with secret "dig-timber-genai/prod/data/mongodb" }}

          mongo_user_id={{ .Data.data.user_mongo }}

          mongo_password={{ .Data.data.pass_mongo }}
          
          {{- end }}
        vault.hashicorp.com/agent-inject-token: "true"
        vault.hashicorp.com/auth-path: auth/dig2-eks-prod
        vault.hashicorp.com/role: timbergenai-prod
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - amd64
                  - key: karpenter.k8s.aws/instance-family
                    operator: In
                    values:
                      - c5
                      - r5
                  - key: karpenter.k8s.aws/instance-size
                    operator: In
                    values:
                      - xlarge
                      - 2xlarge
                      - 4xlarge
                      - 8xlarge
      nodeSelector:
        karpenter.sh/nodepool: timbergenai
      tolerations:
        - key: app
          value: timbergenai
          effect: NoSchedule
      serviceAccountName: pipeline
      imagePullSecrets:
        - name: programintegrityprod-prodrobo-pull-secret
      containers:
        - name: run
          image: quay-prod.elevancehealth.com/programintegrityprod/ica-engine-execute-prompts
          imagePullPolicy: Always
          env:
            - name: PROJECT_NAME
              value: "ica-engine-execute-prompts"
          envFrom:
            - configMapRef:
                name: ica-engine-config
            - configMapRef:
                name: ica-engine-workflow-config
          resources:
            requests:
              memory: "2Gi"
              cpu: "2"
            limits:
              memory: "8Gi"
              cpu: "6"
          volumeMounts:
            - name: anthem-ca-cert
              mountPath: /src/ssl
      volumes:
        - name: anthem-ca-cert
          configMap:
            name: anthem-ca-cert
            items:
              - key: root_chain.pem
                path: root_chain.pem
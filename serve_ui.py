"""
Simple HTTP server to serve the interactive UI.
"""
import http.server
import socketserver
import webbrowser
import os
from urllib.parse import urlparse

# Define the port
PORT = 8080

# Define the handler
class CustomHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Parse the URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        # If the path is '/', serve the interactive_ui.html file
        if path == '/':
            self.path = '/interactive_ui.html'
        
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

def main():
    # Create the server
    with socketserver.TCPServer(("", PORT), CustomHandler) as httpd:
        print(f"Serving at http://localhost:{PORT}")
        
        # Open the browser
        webbrowser.open(f"http://localhost:{PORT}")
        
        # Serve until interrupted
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()

"""
Test script for the logging functionality.
"""

from app.logger import log_execution, log_chunk_result, count_tokens, get_log_files
import json
import time

def test_logging():
    """Test the logging functionality."""
    # Get log file paths
    log_files = get_log_files()
    print(f"Log files: {json.dumps(log_files, indent=2)}")
    
    # Test execution logging
    log_execution("Starting logging test", "INFO")
    log_execution("This is a warning message", "WARNING")
    log_execution("This is an error message", "ERROR")
    log_execution("This is a debug message", "DEBUG")
    
    # Test token counting
    test_text = "This is a test text for token counting. It should have around 15-20 tokens."
    token_count = count_tokens(test_text)
    log_execution(f"Token count for test text: {token_count}", "INFO")
    
    # Test chunk result logging
    chunk_id = "test_file:p1"
    source_info = {
        "source_filename": "test_file.pdf",
        "page_number": 1,
        "spanning_pages": [1]
    }
    prompt = "Extract information from this text: " + test_text
    response = json.dumps({
        "is_relevant": True,
        "extracted_fields": [
            {
                "field_id": "test_field",
                "field_name": "Test Field",
                "extracted_value": "Test Value",
                "confidence": "high",
                "supporting_evidence": "This is a test text"
            }
        ],
        "reasoning": "This is a test reasoning"
    })
    result = json.loads(response)
    
    token_stats = log_chunk_result(
        chunk_id=chunk_id,
        source_info=source_info,
        prompt=prompt,
        response=response,
        result=result
    )
    
    log_execution(f"Token stats: {json.dumps(token_stats, indent=2)}", "INFO")
    
    # Test summary logging
    from app.logger import log_summary
    
    log_summary(
        process_id="test_process",
        total_chunks=10,
        relevant_chunks=5,
        token_stats={
            "prompt_tokens": 1000,
            "response_tokens": 500,
            "total_tokens": 1500,
            "avg_tokens_per_chunk": 150
        },
        elapsed_time=5.5
    )
    
    log_execution("Logging test completed", "INFO")
    
    print("Test completed. Check the log files for results.")

if __name__ == "__main__":
    test_logging()

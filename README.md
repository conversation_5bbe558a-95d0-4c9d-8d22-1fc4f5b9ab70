# BRD-Driven PDF Information Extractor

A system to extract specific information and supporting evidence from uploaded PDF documents based on requirements identified in an uploaded BRD, using OpenAI GPT-4 on text chunks.

## Overview

This application processes Business Requirements Documents (BRD) and PDF files to extract relevant information based on identified requirements. The system:

1. Extracts text from a BRD document (DOC/DOCX)
2. Identifies distinct requirements from the BRD using GPT-4
3. Extracts text from PDF documents, preserving source information
4. Segments PDF text into chunks suitable for GPT-4's context window
5. Processes each chunk against each requirement using GPT-4
6. Aggregates results and generates structured JSON output

## Technical Stack

- **Backend:** Python with FastAPI
- **Frontend:** Streamlit
- **LLM:** OpenAI GPT-4
- **Document Processing:** python-docx, PyPDF2

## Setup Instructions

### Prerequisites

- Python 3.8 or higher
- OpenAI API key with access to GPT-4

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd ica_drg_extraction
   ```

2. Create and activate a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Set up your environment variables:

   Create a `.env` file in the project root by copying the example file:
   ```
   cp .env.example .env
   ```

   Then edit the `.env` file with your specific settings:
   ```
   # OpenAI API settings
   OPENAI_API_KEY=your_openai_api_key_here
   OPENAI_API_BASE=https://api.openai.com/v1
   OPENAI_MODEL=gpt-4

   # SSL Certificate (if needed)
   SSL_CERT_FILE=path/to/your/cert.pem

   # Processing settings
   CHUNK_SIZE=8000
   CHUNK_OVERLAP=200

   # API settings
   API_HOST=0.0.0.0
   API_PORT=8001
   ```

   Alternatively, you can set these as environment variables:
   ```
   export OPENAI_API_KEY=your_api_key_here
   export OPENAI_MODEL=gpt-4
   # ... other variables as needed
   ```

### Running the Application

#### Option 1: FastAPI and Streamlit

1. Start the FastAPI backend:
   ```
   cd ica_drg_extraction
   python -m app.main
   ```

   The API will be available at http://localhost:8000

2. Start the Streamlit frontend (in a separate terminal):
   ```
   cd ica_drg_extraction
   streamlit run frontend/streamlit_app.py
   ```

   The frontend will be available at http://localhost:8501

#### Option 2: Interactive HTML UI

For a more interactive experience with the BRD analysis data:

1. Run the simple HTTP server:
   ```
   cd ica_drg_extraction
   python serve_ui.py
   ```

   This will automatically open the interactive UI in your default browser at http://localhost:8080

#### Option 3: Enhanced HTML UI (Full Application)

For a complete HTML-based application with all the functionality of the Streamlit UI:

1. First, make sure the FastAPI backend is running:
   ```
   cd ica_drg_extraction
   python -m app.main
   ```

2. In a separate terminal, run the enhanced UI server:
   ```
   cd ica_drg_extraction
   python frontend/serve_html_ui.py
   ```

   This will automatically open the enhanced UI in your default browser at http://localhost:8082

   Note: You can also use the root server which will look for files in the frontend/html directory:
   ```
   python serve_enhanced_ui.py
   ```

   The enhanced UI provides:
   - File upload functionality for BRD and PDF files
   - Real-time processing status updates
   - Interactive display of BRD analysis
   - Detailed extraction results
   - PDF viewer with navigation controls
   - JSON download capability

## Usage

1. Open the Streamlit application in your browser
2. Upload a BRD file (DOC or DOCX format)
3. Upload one or more PDF files
4. Click "Start Extraction" to begin the process
5. Wait for the extraction to complete
6. View the results and download the JSON output if needed

## Output Format

The system generates a JSON output with the following structure:

```json
{
  "extracted_data": [
    {
      "requirement_text": "Requirement text from BRD",
      "extracted_info": "Synthesized relevant info from PDFs for this requirement",
      "supporting_evidence": {
        "text": "Exact text snippet from PDF chunk",
        "source_filename": "Source PDF filename",
        "page_number": "Source page number"
      },
      "notes": "Any relevant notes (e.g., multiple sources, partial match, no info found)",
      "found": true
    },
    // ... object for each BRD requirement ...
  ]
}
```

## Project Structure

```
ica_drg_extraction/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application
│   ├── document_processor.py   # Document processing logic
│   ├── extraction_logic.py     # GPT-4 extraction logic
│   ├── utils.py                # Utility functions
│   └── config.py               # Configuration settings
├── frontend/
│   ├── streamlit_app.py        # Streamlit frontend
│   ├── html/                   # HTML UI files
│   │   ├── enhanced_ui.html    # Enhanced HTML UI with full functionality
│   │   ├── enhanced_ui.js      # JavaScript for the enhanced UI
│   │   └── interactive_ui.html # Interactive HTML UI for BRD analysis
│   └── serve_html_ui.py        # Server for HTML UI files
├── tests/
│   ├── __init__.py
│   ├── test_document_processor.py
│   └── test_extraction_logic.py
├── serve_ui.py                 # Simple HTTP server for the interactive UI
├── serve_enhanced_ui.py        # Server for the enhanced UI with API proxy
├── brd_analysis.json           # Sample BRD analysis output
├── .env.example                # Example environment variables
├── .env                        # Environment variables (not in version control)
├── requirements.txt            # Project dependencies
└── README.md                   # This file
```

## Limitations

- The system processes text-based PDFs only
- Processing large documents may take significant time
- The quality of extraction depends on the clarity of the BRD requirements and the PDF content
- API costs may be significant for large documents due to multiple GPT-4 API calls
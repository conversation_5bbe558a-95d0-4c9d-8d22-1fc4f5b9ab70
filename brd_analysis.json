{"project_info": {"title": "Small for Gestational Age (SGA) Validation", "description": "This project focuses on extracting specific data related to the Small for Gestational Age (SGA) condition from medical records, rather than validating criteria.", "objective": "To extract and present relevant data points for SGA from medical records for user validation."}, "data_requirements": [{"id": "doc_sga", "name": "Documentation of SGA", "description": "Extract the term 'Small for Gestational Age' or 'SGA' from provider notes. Populate UI with 'Yes' if present, 'No' if absent.", "type": "select", "required": true, "validation": {"options": ["Yes", "No"]}, "dependencies": [], "ui_settings": {"group": "Documentation", "order": 1, "width": "full", "help_text": "Indicates whether SGA is documented in provider notes."}}, {"id": "biometrics_birth", "name": "Biometrics at Time of Birth", "description": "Extract and display patient's sex, gestational age, weight, head circumference, and length at birth. Convert units if necessary.", "type": "table", "required": true, "validation": {"rules": ["Convert inches to cm, pounds to grams"]}, "dependencies": [], "ui_settings": {"group": "Biometrics", "order": 2, "width": "full", "help_text": "Displays birth biometrics including sex, gestational age, weight, head circumference, and length."}}, {"id": "neonatal_clinical_findings", "name": "Neonatal Clinical Findings", "description": "List of neonatal signs and symptoms extracted from provider notes.", "type": "list", "required": false, "validation": {"options": ["Prematurity", "Premature", "Low birth weight", "Hypoglycemia", "Low blood glucose", "Hypothermia", "Failed car seat challenge"]}, "dependencies": [], "ui_settings": {"group": "Neonatal Clinical Findings", "order": 3, "width": "full", "help_text": "Lists neonatal clinical findings from provider notes."}}, {"id": "neonatal_conditions", "name": "Neonatal Conditions", "description": "List of neonatal conditions extracted from provider notes.", "type": "list", "required": false, "validation": {"options": ["Fetal Growth Restriction (FGR)", "Intrauterine Growth Restriction (IUGR)", "<PERSON>cephaly", "Cytomegalovirus (CMV)"]}, "dependencies": [], "ui_settings": {"group": "Neonatal Conditions", "order": 4, "width": "full", "help_text": "Lists neonatal conditions from provider notes."}}, {"id": "other_head_circumference", "name": "Other Head Circumference Post Birth", "description": "Extract head circumference measurements post-birth with date and value.", "type": "list", "required": false, "validation": {"rules": ["Date format MM-DD-YYYY"]}, "dependencies": [], "ui_settings": {"group": "Other Head Circumference", "order": 5, "width": "full", "help_text": "Displays head circumference measurements post-birth with dates."}}, {"id": "maternal_conditions", "name": "Maternal Contributing Conditions", "description": "List of maternal conditions from medical history extracted from provider notes.", "type": "list", "required": false, "validation": {"options": ["Oligohydramnios", "Pre-eclampsia", "Maternal (illicit) drug use", "Maternal hypertension", "Cytomegalovirus (CMV) (current)", "Multiple gestation", "Substance use"]}, "dependencies": [], "ui_settings": {"group": "Maternal Conditions", "order": 6, "width": "full", "help_text": "Lists maternal conditions contributing to SGA."}}], "business_rules": [], "output_formats": [{"name": "UI Display", "description": "Primary user interface display for SGA data extraction.", "fields": ["doc_sga", "biometrics_birth", "neonatal_clinical_findings", "neonatal_conditions", "other_head_circumference", "maternal_conditions"]}]}
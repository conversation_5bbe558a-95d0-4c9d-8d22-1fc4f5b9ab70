"""
Simple HTTP server to serve the HTML UI from the frontend/html directory.
"""
import http.server
import socketserver
import webbrowser
import os
import urllib.request
import urllib.error
from urllib.parse import urlparse, unquote
import mimetypes
import ssl

# Define the ports
UI_PORT = 8082  # Using a different port to avoid conflicts
API_PORT = 8001  # FastAPI backend port

# Define the API URL
API_URL = f"http://localhost:{API_PORT}"

# Define the UI directory (relative to this script)
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
UI_DIR = os.path.join(SCRIPT_DIR, "html")

# Configure SSL context to ignore certificate validation if needed
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

# Define the handler
class CustomHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        kwargs['directory'] = UI_DIR
        super().__init__(*args, **kwargs)

    def do_GET(self):
        # Parse the URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # If the path is '/', serve the enhanced_ui.html file
        if path == '/':
            self.path = '/enhanced_ui.html'
            return http.server.SimpleHTTPRequestHandler.do_GET(self)

        # If the path starts with '/api/download-pdf/', handle PDF download
        elif path.startswith('/api/download-pdf/'):
            filename = unquote(path[len('/api/download-pdf/'):])
            self.handle_pdf_download(filename)
            return

        # If the path starts with '/api/', proxy the request to the FastAPI backend
        elif path.startswith('/api/'):
            api_path = path[4:]  # Remove '/api' prefix
            self.proxy_request('GET', api_path, None)
            return

        # Otherwise, serve the file as usual
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

    def do_POST(self):
        # Parse the URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # If the path starts with '/api/', proxy the request to the FastAPI backend
        if path.startswith('/api/'):
            api_path = path[4:]  # Remove '/api' prefix

            # Get the request body
            content_length = int(self.headers['Content-Length'])
            body = self.rfile.read(content_length)

            # Proxy the request
            self.proxy_request('POST', api_path, body)
            return

        # Otherwise, return 404
        self.send_error(404, 'Not Found')

    def handle_pdf_download(self, filename):
        """Handle PDF download requests by proxying to the FastAPI backend."""
        try:
            # Construct the API URL for the PDF file
            url = f"{API_URL}/download/{filename}"
            print(f"Proxying PDF download request to: {url}")

            # Create the request
            req = urllib.request.Request(url, method='GET')

            # Make the request
            try:
                with urllib.request.urlopen(req, context=ssl_context) as response:
                    # Get the response status and headers
                    status = response.status
                    headers = response.getheaders()

                    # Get the response body (PDF content)
                    pdf_content = response.read()

                    # Send the response status
                    self.send_response(status)

                    # Set appropriate headers for PDF
                    self.send_header('Content-Type', 'application/pdf')
                    self.send_header('Content-Disposition', f'inline; filename="{filename}"')
                    self.send_header('Content-Length', len(pdf_content))

                    # Copy other relevant headers
                    for header, value in headers:
                        if header.lower() not in ['content-type', 'content-disposition', 'content-length', 'transfer-encoding', 'connection']:
                            self.send_header(header, value)

                    self.end_headers()

                    # Send the PDF content
                    self.wfile.write(pdf_content)
                    print(f"Successfully served PDF: {filename} ({len(pdf_content)} bytes)")
            except urllib.error.HTTPError as e:
                print(f"HTTP Error: {e.code} - {e.reason} for URL: {url}")
                if e.code == 404:
                    # Try with a process prefix
                    print("PDF not found, trying to find it with process prefix...")
                    # This is a fallback for when the file might have a process prefix
                    self.send_error(404, f"PDF file not found: {filename}")
                else:
                    # Forward the error
                    self.send_response(e.code)
                    for header, value in e.headers.items():
                        if header.lower() not in ['transfer-encoding', 'connection']:
                            self.send_header(header, value)
                    self.end_headers()
                    self.wfile.write(e.read())

        except urllib.error.HTTPError as e:
            # Handle HTTP errors
            self.send_response(e.code)
            for header, value in e.headers.items():
                if header.lower() not in ['transfer-encoding', 'connection']:
                    self.send_header(header, value)
            self.end_headers()
            self.wfile.write(e.read())

        except Exception as e:
            # Handle other errors
            self.send_error(500, f"Error serving PDF: {str(e)}")

    def proxy_request(self, method, path, body):
        try:
            # Construct the API URL
            url = f"{API_URL}/{path}"

            # Create the request
            req = urllib.request.Request(url, data=body, method=method)

            # Copy headers from the original request
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'content-length']:
                    req.add_header(header, value)

            # Make the request
            with urllib.request.urlopen(req, context=ssl_context) as response:
                # Get the response status and headers
                status = response.status
                headers = response.getheaders()

                # Get the response body
                response_body = response.read()

                # Send the response status and headers
                self.send_response(status)
                for header, value in headers:
                    if header.lower() not in ['transfer-encoding', 'connection']:
                        self.send_header(header, value)
                self.end_headers()

                # Send the response body
                self.wfile.write(response_body)

        except urllib.error.HTTPError as e:
            # Handle HTTP errors
            self.send_response(e.code)
            for header, value in e.headers.items():
                if header.lower() not in ['transfer-encoding', 'connection']:
                    self.send_header(header, value)
            self.end_headers()
            self.wfile.write(e.read())

        except Exception as e:
            # Handle other errors
            self.send_error(500, str(e))

def main():
    # Add MIME type for JavaScript
    mimetypes.add_type('application/javascript', '.js')

    # Check if UI directory exists
    if not os.path.exists(UI_DIR):
        print(f"Error: UI directory not found: {UI_DIR}")
        print("Please make sure the HTML UI files are in the frontend/html directory.")
        return

    print(f"UI directory: {UI_DIR}")
    print(f"Files in UI directory: {os.listdir(UI_DIR)}")

    # Create the server
    with socketserver.TCPServer(("", UI_PORT), CustomHandler) as httpd:
        print(f"Serving HTML UI at http://localhost:{UI_PORT}")
        print(f"API requests will be proxied to {API_URL}")

        # Open the browser
        webbrowser.open(f"http://localhost:{UI_PORT}")

        # Serve until interrupted
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()

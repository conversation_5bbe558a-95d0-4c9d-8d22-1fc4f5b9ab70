"""
Streamlit frontend for the PDF Information Extractor.
"""
import json
import time
import requests
import base64
import streamlit as st
from io import BytesIO


# API endpoint configuration
API_URL = "http://localhost:8001"  # FastAPI backend URL


def display_pdf(pdf_url):
    """
    Display a PDF file in the Streamlit app using multiple methods for compatibility.

    Args:
        pdf_url: URL to the PDF file
    """
    # First, try to fetch the PDF content directly
    try:
        # Fetch the PDF content
        response = requests.get(pdf_url)

        if response.status_code == 200:
            # Use Streamlit's native PDF display with base64 encoding
            pdf_content = response.content

            # Display the PDF directly in Streamlit using base64 encoding
            base64_pdf = base64.b64encode(pdf_content).decode('utf-8')

            # Create a styled iframe with better appearance
            pdf_display_native = f'''
            <iframe
                src="data:application/pdf;base64,{base64_pdf}"
                width="100%"
                height="800"
                style="border: 1px solid #ddd; border-radius: 8px; padding: 5px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                type="application/pdf">
            </iframe>
            '''

            # Display the PDF
            st.markdown(pdf_display_native, unsafe_allow_html=True)
            return
    except Exception as e:
        st.warning(f"Could not display PDF using base64 encoding: {str(e)}")

    # Method 1: Use PDF.js viewer (more reliable)
    # This uses Mozilla's PDF.js to render the PDF
    pdf_display = f"""
    <div style="display: flex; justify-content: center; width: 100%;">
        <iframe
            src="https://mozilla.github.io/pdf.js/web/viewer.html?file={pdf_url}"
            width="100%"
            height="800"
            style="border: 1px solid #ddd; border-radius: 8px; padding: 5px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
        >
        </iframe>
    </div>
    """

    # Method 2: Direct embedding (fallback)
    direct_embed = f"""
    <div style="display: flex; justify-content: center; width: 100%;">
        <embed
            src="{pdf_url}"
            type="application/pdf"
            width="100%"
            height="800"
            style="border: 1px solid #ddd; border-radius: 8px; padding: 5px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
        >
    </div>
    """

    # Method 3: Object tag (another fallback)
    object_tag = f"""
    <div style="display: flex; justify-content: center; width: 100%;">
        <object
            data="{pdf_url}"
            type="application/pdf"
            width="100%"
            height="800"
            style="border: 1px solid #ddd; border-radius: 8px; padding: 5px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
        >
            <p>It appears your browser doesn't support embedded PDFs.
            You can <a href="{pdf_url}" target="_blank">download the PDF</a> instead.</p>
        </object>
    </div>
    """

    # Try each method in sequence until one works
    try:
        st.markdown(pdf_display, unsafe_allow_html=True)
    except Exception as e:
        st.warning(f"PDF.js viewer failed: {str(e)}. Trying direct embedding...")

        try:
            st.markdown(direct_embed, unsafe_allow_html=True)
        except Exception as e2:
            st.warning(f"Direct embedding failed: {str(e2)}. Trying object tag...")

            try:
                st.markdown(object_tag, unsafe_allow_html=True)
            except Exception as e3:
                st.error(f"All PDF display methods failed. Please download the PDF instead.")
                st.markdown(f"[Download PDF]({pdf_url})")

    # Add a note about PDF viewing
    st.caption("If the PDF doesn't display correctly, you can download it using the button above.")


def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="ICA Information Extractor",
        page_icon="📄",
        layout="wide"
    )

    # Create a sidebar for application overview and file uploads
    with st.sidebar:
        st.title("📄 ICA Information Extractor")
        st.markdown(
            """
            This application extracts specific information from PDF documents based on requirements
            identified in a Business Requirements Document (BRD).
            """
        )

        # Instructions in an expander
        with st.expander("📋 Instructions", expanded=True):
            st.markdown(
                """
                1. Upload a BRD file (`.doc` or `.docx`)
                2. Upload one or more PDF files
                3. Click "Start Extraction" to begin the process
                4. View the extracted information and PDF documents
                """
            )

        # File upload section
        st.header("Upload Files")

        # BRD Document upload
        st.subheader("BRD Document")
        brd_file = st.file_uploader(
            "Upload BRD file (DOC or DOCX)",
            type=["doc", "docx"],
            help="Upload a Business Requirements Document (BRD) in DOC or DOCX format."
        )

        # PDF Documents upload
        st.subheader("PDF Documents")
        pdf_files = st.file_uploader(
            "Upload PDF files",
            type=["pdf"],
            accept_multiple_files=True,
            help="Upload one or more PDF files to extract information from."
        )

        # Process button
        start_button = st.button(
            "🚀 Start Extraction",
            disabled=not (brd_file and pdf_files),
            use_container_width=True
        )

        # Show validation messages
        if not brd_file:
            st.info("Please upload a BRD file.")
        if not pdf_files:
            st.info("Please upload at least one PDF file.")

    # Main content area
    st.title("ICA Information Extractor")

    # Process files if button is clicked
    if start_button:
        if not brd_file or not pdf_files:
            st.error("Please upload both a BRD file and at least one PDF file.")
        else:
            # Start the extraction process
            process_id = start_extraction(brd_file, pdf_files)

            if process_id:
                # Create a placeholder for the status
                status_placeholder = st.empty()

                # Poll for results
                result = poll_for_results(process_id, status_placeholder)

                if result and result.get("status") == "completed":
                    display_results(result.get("result", {}))
            else:
                st.error("Failed to start the extraction process. Please check the API connection.")


def start_extraction(brd_file, pdf_files):
    """
    Start the extraction process by uploading files to the API.

    Args:
        brd_file: The BRD file
        pdf_files: List of PDF files

    Returns:
        str: Process ID if successful, None otherwise
    """
    with st.spinner("Uploading files..."):
        try:
            # Prepare files for upload
            files = {
                "brd_file": (brd_file.name, brd_file.getvalue(), "application/octet-stream"),
            }

            # Add each PDF file with the same key (FastAPI will handle this as a list)
            for pdf_file in pdf_files:
                files["pdf_files"] = (pdf_file.name, pdf_file.getvalue(), "application/octet-stream")

            # Make API request
            response = requests.post(
                f"{API_URL}/upload-and-process/",
                files=files
            )

            if response.status_code == 200:
                data = response.json()
                return data.get("process_id")
            else:
                st.error(f"API Error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            st.error(f"Error starting extraction: {str(e)}")
            return None


def poll_for_results(process_id, status_placeholder):
    """
    Poll the API for extraction results.

    Args:
        process_id: The process ID
        status_placeholder: Streamlit placeholder for status updates

    Returns:
        dict: Extraction results if successful, None otherwise
    """
    max_polls = 100  # Maximum number of polling attempts
    poll_interval = 2  # Seconds between polls

    # Create a progress bar
    progress_bar = st.progress(0)

    # Create container for detailed status information
    details_container = st.expander("Processing Details", expanded=False)

    for i in range(max_polls):
        try:
            # Make API request
            response = requests.get(f"{API_URL}/status/{process_id}")

            if response.status_code == 200:
                data = response.json()
                status = data.get("status", "unknown")
                message = data.get("message", "No status message")
                progress = data.get("progress", 0)

                # Update progress bar
                progress_bar.progress(progress / 100)

                # Update status display with color-coded message
                if status == "processing":
                    status_placeholder.info(f"⏳ {message}")
                elif status == "completed":
                    status_placeholder.success(f"✅ {message}")
                elif status == "failed":
                    status_placeholder.error(f"❌ {message}")
                else:
                    status_placeholder.warning(f"⚠️ Status: {status} - {message}")

                # Display detailed information if available
                with details_container:
                    # Clear previous content
                    details_container.empty()

                    # Display step information if available
                    current_step = data.get("current_step")
                    total_steps = data.get("total_steps")
                    if current_step and total_steps:
                        st.write(f"**Current Step:** {current_step}/{total_steps}")

                    # Display additional details if available
                    details = data.get("details", {})
                    if details:
                        st.write("**Processing Details:**")
                        for key, value in details.items():
                            # Format the key for better display
                            formatted_key = key.replace("_", " ").title()
                            st.write(f"- {formatted_key}: {value}")

                # If processing is complete or failed, return the result
                if status in ["completed", "failed"]:
                    if status == "completed":
                        status_placeholder.success("✅ Extraction completed successfully!")
                        progress_bar.progress(100)
                    else:
                        status_placeholder.error(f"❌ Extraction failed: {data.get('error', 'Unknown error')}")
                        progress_bar.empty()

                    return data
            else:
                status_placeholder.error(f"API Error: {response.status_code} - {response.text}")
                progress_bar.empty()
                return None

        except Exception as e:
            status_placeholder.error(f"Error polling for results: {str(e)}")
            progress_bar.empty()
            return None

        # Wait before next poll
        time.sleep(poll_interval)

    status_placeholder.warning("⚠️ Polling timeout reached. The extraction process may still be running.")
    return None


def display_results(result):
    """
    Display extraction results.

    Args:
        result: Extraction results dictionary
    """
    if not result:
        st.warning("No results to display.")
        return

    st.header("Extraction Results")

    # Get the extracted data
    extracted_data = result.get("extracted_data", [])

    if not extracted_data:
        st.warning("No data was extracted from the documents.")
        return

    # Check if we're using the consolidated requirement approach (should be just one item)
    is_consolidated = len(extracted_data) == 1 and "key_points" in extracted_data[0]

    # Display summary
    st.subheader("Summary")

    if is_consolidated:
        # For BRD analysis
        item = extracted_data[0]
        key_points = item.get("key_points", [])
        addressed_points = item.get("addressed_points", [])

        # Count how many key points were addressed
        addressed_count = sum(1 for point in addressed_points if point in key_points)

        # Create a progress bar for key points addressed
        st.markdown("### Key Points Coverage")

        if key_points:
            # Calculate percentage of key points addressed
            percentage = addressed_count / len(key_points) * 100 if key_points else 0

            # Create a progress bar
            st.progress(percentage / 100)

            # Display metrics in columns
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "Information Found",
                    "Yes" if item.get("found", False) else "No",
                    help="Whether relevant information was found in the PDFs"
                )

            with col2:
                st.metric(
                    "Total Key Points",
                    f"{len(key_points)}",
                    help="Total number of key points extracted from the BRD"
                )

            with col3:
                st.metric(
                    "Key Points Addressed",
                    f"{addressed_count}",
                    help="Number of key points from the BRD that were addressed in the PDFs"
                )

            with col4:
                st.metric(
                    "Coverage Percentage",
                    f"{percentage:.1f}%",
                    help="Percentage of key points from the BRD that were addressed in the PDFs"
                )

            # Add a note about the coverage
            if percentage == 100:
                st.success("All key points from the BRD were addressed in the documents!")
            elif percentage > 75:
                st.info(f"Most key points ({percentage:.1f}%) from the BRD were addressed in the documents.")
            elif percentage > 50:
                st.warning(f"Only {percentage:.1f}% of key points from the BRD were addressed in the documents.")
            else:
                st.error(f"Less than half ({percentage:.1f}%) of key points from the BRD were addressed in the documents.")
    else:
        # For multiple requirements (original approach)
        total_requirements = len(extracted_data)
        found_requirements = sum(1 for item in extracted_data if item.get("found", False))

        st.metric(
            "Requirements Processed",
            f"{total_requirements}",
            help="Total number of requirements identified in the BRD"
        )

        st.metric(
            "Requirements Found",
            f"{found_requirements} ({found_requirements/total_requirements*100:.1f}%)" if total_requirements > 0 else "0 (0%)",
            help="Number of requirements for which information was found in the PDFs"
        )

    # Display PDF documents if available
    if "pdf_files" in result:
        st.markdown("---")

        # Create a more prominent section for PDFs
        st.header("📄 PDF Documents")

        pdf_files = result.get("pdf_files", [])

        if pdf_files:
            # Create two columns for controls
            control_col1, control_col2 = st.columns([3, 1])

            with control_col1:
                # Create a selectbox for PDF selection
                pdf_options = [f"{pdf['original_filename']}" for pdf in pdf_files]
                selected_pdf_index = st.selectbox(
                    "Select a PDF to view:",
                    range(len(pdf_options)),
                    format_func=lambda i: pdf_options[i]
                )

            with control_col2:
                # Create a more prominent toggle button for showing/hiding PDFs
                show_pdfs = st.checkbox("📄 Show/Hide PDF", value=True)

            # Get the selected PDF
            selected_pdf = pdf_files[selected_pdf_index]
            pdf_url = f"{API_URL}{selected_pdf['url_path']}"

            # Create a container for PDF information
            pdf_info = st.container()

            with pdf_info:
                # Display PDF information
                st.markdown(f"**Currently Selected:** {selected_pdf['original_filename']}")

                # Add a download button for the PDF
                response = requests.get(pdf_url)
                if response.status_code == 200:
                    pdf_content = response.content
                    st.download_button(
                        label=f"📥 Download {selected_pdf['original_filename']}",
                        data=pdf_content,
                        file_name=selected_pdf['original_filename'],
                        mime="application/pdf"
                    )

            # Display the PDF if toggle is on
            if show_pdfs:
                # Create a container with a border for the PDF
                pdf_container = st.container()

                # Add a visual separator
                st.markdown("<hr style='margin-top: 0; margin-bottom: 20px;'>", unsafe_allow_html=True)

                with pdf_container:
                    display_pdf(pdf_url)
            else:
                # Show a message when PDF is hidden
                st.info("📄 PDF viewer is currently hidden. Check the 'Show/Hide PDF' box above to display the document.")
        else:
            st.info("No PDF files available for viewing.")

    # Display detailed results
    st.markdown("---")
    st.subheader("Detailed Results")

    for i, item in enumerate(extracted_data):
        # Create a container for the requirement
        with st.container():
            # Determine the title for the requirement section
            if is_consolidated:
                st.markdown(f"### BRD Analysis: {item.get('requirement_id', 'ANALYSIS-BRD-001')}")
            else:
                st.markdown(f"### Requirement {i+1}")

            # Create tabs for different sections
            main_tab, key_points_tab, extraction_tab = st.tabs([
                "Main Objective & Context",
                "Key Points",
                "Extracted Information"
            ])

            # Tab 1: Main Objective & Context
            with main_tab:
                # Display main objective information
                st.markdown(f"**Main Objective:**")
                st.markdown(f"{item['requirement_text']}")

                # For consolidated requirement, show context summary
                if is_consolidated and item.get("context"):
                    st.markdown("---")
                    st.markdown(f"**Context Summary:**")
                    st.markdown(f"{item['context']}")

            # Tab 2: Key Points
            with key_points_tab:
                # Display all key points from BRD
                if is_consolidated and item.get("key_points"):
                    st.markdown("#### All Key Points from BRD")

                    # Create a container for the key points
                    key_points_container = st.container()

                    with key_points_container:
                        # Create columns for better display
                        cols = st.columns(2)

                        # Split key points between columns
                        key_points = item.get("key_points", [])
                        half_point = len(key_points) // 2 + len(key_points) % 2

                        # First column
                        with cols[0]:
                            for j, point in enumerate(key_points[:half_point]):
                                # Check if this point was addressed
                                addressed = point in item.get("addressed_points", [])
                                icon = "✅" if addressed else "❌"
                                st.markdown(f"**{icon} {j+1}.** {point}")

                        # Second column
                        with cols[1]:
                            for j, point in enumerate(key_points[half_point:], start=half_point):
                                # Check if this point was addressed
                                addressed = point in item.get("addressed_points", [])
                                icon = "✅" if addressed else "❌"
                                st.markdown(f"**{icon} {j+1}.** {point}")
                else:
                    st.info("No key points available for this requirement.")

            # Tab 3: Extracted Information
            with extraction_tab:
                if item.get("found", False):
                    # Display extracted information
                    st.markdown("#### Extracted Information")
                    st.markdown(item['extracted_info'])

                    # Display grouped evidence
                    grouped_evidence = item.get("grouped_evidence", {})
                    if grouped_evidence:
                        st.markdown("---")
                        st.markdown("#### All Supporting Evidence")

                        # Create tabs for each field
                        field_ids = list(grouped_evidence.keys())
                        if field_ids:
                            # Create tabs for each field (sorted alphabetically for consistency)
                            sorted_field_ids = sorted(field_ids)
                            tabs = st.tabs([f"{field_id} ({len(grouped_evidence[field_id])} items)" for field_id in sorted_field_ids])

                            # Display evidence for each field in its tab
                            for i, field_id in enumerate(sorted_field_ids):
                                with tabs[i]:
                                    evidence_list = grouped_evidence[field_id]

                                    # Show a message about evidence filtering
                                    if len(evidence_list) > 0:
                                        st.info(f"Showing {len(evidence_list)} pieces of evidence for '{field_id}'. Similar evidences have been filtered out and results are sorted by confidence.")

                                    # Create an expander for each piece of evidence
                                    for j, evidence in enumerate(evidence_list):
                                        # Get page number, removing UNKNOWN if present
                                        page_number = evidence.get('page_number', '')
                                        if page_number == 'UNKNOWN':
                                            page_number = ''

                                        # Get evidence text for the expander title
                                        evidence_text = evidence.get('text', '')
                                        # Truncate if too long
                                        if len(evidence_text) > 80:
                                            evidence_text = evidence_text[:77] + "..."

                                        with st.expander(f"Page {page_number} - {evidence_text}", expanded=(j==0)):
                                            # Get evidence source information
                                            source_filename = evidence.get('source_filename', 'Unknown')
                                            page_number = evidence.get('page_number', 'Unknown')

                                            # Check if we have PDF files available
                                            if "pdf_files" in result:
                                                pdf_files = result.get("pdf_files", [])

                                                # Find the matching PDF file
                                                matching_pdf = None
                                                for pdf in pdf_files:
                                                    if pdf['original_filename'] == source_filename:
                                                        matching_pdf = pdf
                                                        break

                                                if matching_pdf:
                                                    # Create a link to the PDF
                                                    pdf_url = f"{API_URL}{matching_pdf['url_path']}"

                                                    # Display source with link to PDF
                                                    st.markdown(f"**Source:** {source_filename}, Page {page_number} - [View PDF]({pdf_url})")
                                                else:
                                                    st.markdown(f"**Source:** {source_filename}, Page {page_number}")
                                            else:
                                                st.markdown(f"**Source:** {source_filename}, Page {page_number}")

                                            # Display the evidence text
                                            st.markdown("**Text:**")
                                            st.markdown(f"*{evidence.get('text', 'No text provided')}*")

                    # Display single supporting evidence (for backward compatibility)
                    elif item.get("supporting_evidence", {}):
                        evidence = item.get("supporting_evidence", {})
                        st.markdown("---")
                        st.markdown("#### Supporting Evidence")

                        # Get evidence source information
                        source_filename = evidence.get('source_filename', 'Unknown')
                        page_number = evidence.get('page_number', 'Unknown')

                        # Check if we have PDF files available
                        if "pdf_files" in result:
                            pdf_files = result.get("pdf_files", [])

                            # Find the matching PDF file
                            matching_pdf = None
                            for pdf in pdf_files:
                                if pdf['original_filename'] == source_filename:
                                    matching_pdf = pdf
                                    break

                            if matching_pdf:
                                # Create a link to the PDF
                                pdf_url = f"{API_URL}{matching_pdf['url_path']}"

                                # Display source with link to PDF
                                st.markdown(f"**Source:** {source_filename}, Page {page_number} - [View PDF]({pdf_url})")
                            else:
                                st.markdown(f"**Source:** {source_filename}, Page {page_number}")
                        else:
                            st.markdown(f"**Source:** {source_filename}, Page {page_number}")

                        # Display the evidence text
                        st.markdown("**Text:**")
                        st.markdown(f"*{evidence.get('text', 'No text provided')}*")

                    # For consolidated requirement, show detailed analysis of addressed key points
                    if is_consolidated and item.get("addressed_points"):
                        st.markdown("---")
                        st.markdown("#### Detailed Analysis of Addressed Key Points")

                        # Get reasoning for each key point
                        reasoning = item.get("reasoning", {})
                        addressed_points = item.get("addressed_points", [])

                        if addressed_points:
                            # Create an expander for each addressed key point
                            for j, point in enumerate(addressed_points):
                                point_num = item.get("key_points", []).index(point) + 1 if point in item.get("key_points", []) else j + 1

                                with st.expander(f"✅ Point {point_num}: {point}", expanded=False):
                                    # Display reasoning if available
                                    if point in reasoning:
                                        point_data = reasoning[point]

                                        # Check if we have the new structure or the old string format
                                        if isinstance(point_data, dict):
                                            # New structure with reasoning_text and evidence
                                            reasoning_text = point_data.get("reasoning_text", "")
                                            point_evidence_list = point_data.get("evidence", [])

                                            # Display reasoning text
                                            st.markdown("**Analysis and Reasoning:**")
                                            st.markdown(reasoning_text)

                                            # Display all evidence for this point
                                            if point_evidence_list:
                                                st.markdown("---")
                                                st.markdown(f"**Supporting Evidence ({len(point_evidence_list)} items):**")

                                                # Create an accordion for each piece of evidence
                                                for i, evidence in enumerate(point_evidence_list):
                                                    # Get page number, removing UNKNOWN if present
                                                    page_number = evidence.get('page_number', '')
                                                    if page_number == 'UNKNOWN':
                                                        page_number = ''

                                                    # Get evidence text for the expander title
                                                    evidence_text = evidence.get('text', '')
                                                    # Truncate if too long
                                                    if len(evidence_text) > 80:
                                                        evidence_text = evidence_text[:77] + "..."

                                                    with st.expander(f"Page {page_number} - {evidence_text}", expanded=(i==0)):
                                                        # Get evidence source information
                                                        source_filename = evidence.get('source_filename', 'Unknown')
                                                        page_number = evidence.get('page_number', 'Unknown')

                                                        # Check if we have PDF files available
                                                        if "pdf_files" in result:
                                                            pdf_files = result.get("pdf_files", [])

                                                            # Find the matching PDF file
                                                            matching_pdf = None
                                                            for pdf in pdf_files:
                                                                if pdf['original_filename'] == source_filename:
                                                                    matching_pdf = pdf
                                                                    break

                                                            if matching_pdf:
                                                                # Create a link to the PDF
                                                                pdf_url = f"{API_URL}{matching_pdf['url_path']}"

                                                                # Display source with link to PDF
                                                                st.markdown(f"From: {source_filename}, Page {page_number} - [View PDF]({pdf_url})")
                                                            else:
                                                                st.markdown(f"From: {source_filename}, Page {page_number}")
                                                        else:
                                                            st.markdown(f"From: {source_filename}, Page {page_number}")

                                                        # Show the evidence text
                                                        evidence_text = evidence.get("text", "")
                                                        if evidence_text:
                                                            st.markdown("**Text:**")
                                                            st.markdown(f"*{evidence_text}*")
                                            else:
                                                st.info("No supporting evidence found for this key point.")
                                        else:
                                            # Old string format for backward compatibility
                                            st.markdown("**Evidence and Reasoning:**")
                                            st.markdown(point_data)

                                            # Add a divider
                                            st.markdown("---")

                                            # Add a link to the supporting evidence
                                            evidence = item.get("supporting_evidence", {})
                                            if evidence and evidence.get("text"):
                                                st.markdown("**Related Evidence:**")

                                                # Get evidence source information
                                                source_filename = evidence.get('source_filename', 'Unknown')
                                                page_number = evidence.get('page_number', 'Unknown')

                                                # Check if we have PDF files available
                                                if "pdf_files" in result:
                                                    pdf_files = result.get("pdf_files", [])

                                                    # Find the matching PDF file
                                                    matching_pdf = None
                                                    for pdf in pdf_files:
                                                        if pdf['original_filename'] == source_filename:
                                                            matching_pdf = pdf
                                                            break

                                                    if matching_pdf:
                                                        # Create a link to the PDF
                                                        pdf_url = f"{API_URL}{matching_pdf['url_path']}"

                                                        # Display source with link to PDF
                                                        st.markdown(f"From: {source_filename}, Page {page_number} - [View PDF]({pdf_url})")
                                                    else:
                                                        st.markdown(f"From: {source_filename}, Page {page_number}")
                                                else:
                                                    st.markdown(f"From: {source_filename}, Page {page_number}")

                                                # Show a snippet of the evidence
                                                evidence_text = evidence.get("text", "")
                                                if evidence_text:
                                                    st.markdown(f"*{evidence_text}*")
                                    else:
                                        st.info("No detailed reasoning available for this key point.")
                        else:
                            st.warning("No key points were addressed in the documents.")

                    # Display notes
                    if item.get("notes"):
                        st.markdown("---")
                        st.info(item["notes"])
                else:
                    st.warning("No relevant information found for this requirement.")

    # Download section
    st.markdown("---")
    st.subheader("Download Results")

    # Create columns for the download button
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        # Download button for JSON results
        st.download_button(
            label="📥 Download Results (JSON)",
            data=json.dumps(result, indent=2),
            file_name="extraction_results.json",
            mime="application/json",
            use_container_width=True
        )


if __name__ == "__main__":
    main()
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BRD Analysis Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .sidebar {
            background-color: var(--secondary-color);
            color: white;
            min-height: 100vh;
            padding: 20px 0;
            position: sticky;
            top: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 20px;
            margin: 5px 0;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
            transition: transform 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        .requirement-card {
            cursor: pointer;
        }
        
        .requirement-details {
            display: none;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .badge-custom {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }
        
        .validation-rule {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            border-left: 3px solid var(--primary-color);
        }
        
        .business-rule {
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--warning-color);
        }
        
        .output-format {
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--success-color);
        }
        
        .validation-status {
            padding: 20px;
            border-radius: 8px;
            background-color: #f8f9fa;
            margin-top: 20px;
        }
        
        .validation-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .validation-item i {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        .validation-item.valid i {
            color: var(--success-color);
        }
        
        .validation-item.invalid i {
            color: var(--danger-color);
        }
        
        .validation-item.pending i {
            color: var(--warning-color);
        }
        
        .progress {
            height: 10px;
            border-radius: 5px;
            margin-top: 5px;
        }
        
        .dashboard-header {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        
        .dashboard-title {
            color: var(--secondary-color);
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .dashboard-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .summary-card {
            text-align: center;
            padding: 20px;
        }
        
        .summary-card i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .summary-card .number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--secondary-color);
        }
        
        .summary-card .label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="d-flex align-items-center mb-4 px-3">
                    <i class="bi bi-clipboard-data fs-3 me-2"></i>
                    <h5 class="mb-0">BRD Analysis</h5>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard" data-bs-toggle="tab">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#requirements" data-bs-toggle="tab">
                            <i class="bi bi-list-check"></i> Requirements
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#business-rules" data-bs-toggle="tab">
                            <i class="bi bi-diagram-3"></i> Business Rules
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#output-formats" data-bs-toggle="tab">
                            <i class="bi bi-file-earmark-text"></i> Output Formats
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#validation" data-bs-toggle="tab">
                            <i class="bi bi-check-circle"></i> Validation
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="tab-content">
                    <!-- Dashboard Tab -->
                    <div class="tab-pane fade show active" id="dashboard">
                        <div class="dashboard-header">
                            <h1 class="dashboard-title" id="project-title">Loading...</h1>
                            <p class="dashboard-subtitle" id="project-description">Loading...</p>
                            <p class="text-muted" id="project-objective">Loading...</p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card summary-card">
                                    <i class="bi bi-list-check"></i>
                                    <div class="number" id="requirements-count">0</div>
                                    <div class="label">Requirements</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card summary-card">
                                    <i class="bi bi-diagram-3"></i>
                                    <div class="number" id="rules-count">0</div>
                                    <div class="label">Business Rules</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card summary-card">
                                    <i class="bi bi-file-earmark-text"></i>
                                    <div class="number" id="outputs-count">0</div>
                                    <div class="label">Output Formats</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-4">
                            <div class="card-header">
                                <i class="bi bi-check-circle me-2"></i> Validation Status
                            </div>
                            <div class="card-body">
                                <div class="progress mb-3">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 0%" id="validation-progress"></div>
                                </div>
                                <div id="validation-summary">
                                    <!-- Validation summary will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Requirements Tab -->
                    <div class="tab-pane fade" id="requirements">
                        <h2 class="mb-4">Data Requirements</h2>
                        <div class="row" id="requirements-container">
                            <!-- Requirements will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Business Rules Tab -->
                    <div class="tab-pane fade" id="business-rules">
                        <h2 class="mb-4">Business Rules</h2>
                        <div id="business-rules-container">
                            <!-- Business rules will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Output Formats Tab -->
                    <div class="tab-pane fade" id="output-formats">
                        <h2 class="mb-4">Output Formats</h2>
                        <div id="output-formats-container">
                            <!-- Output formats will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Validation Tab -->
                    <div class="tab-pane fade" id="validation">
                        <h2 class="mb-4">Validation Checklist</h2>
                        <div class="card">
                            <div class="card-header">
                                <i class="bi bi-clipboard-check me-2"></i> Validation Criteria
                            </div>
                            <div class="card-body">
                                <div id="validation-checklist">
                                    <!-- Validation checklist will be populated here -->
                                </div>
                                
                                <div class="validation-status mt-4">
                                    <h5>Overall Validation Status</h5>
                                    <div class="alert alert-warning" id="validation-result">
                                        <i class="bi bi-exclamation-triangle me-2"></i> Validation pending
                                    </div>
                                    <button class="btn btn-primary" id="validate-btn">Run Validation</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load BRD analysis data
        fetch('brd_analysis.json')
            .then(response => response.json())
            .then(data => {
                // Initialize the UI with the data
                initializeUI(data);
            })
            .catch(error => {
                console.error('Error loading BRD analysis data:', error);
                alert('Failed to load BRD analysis data. Please check the console for details.');
            });
            
        function initializeUI(data) {
            // Populate project information
            document.getElementById('project-title').textContent = data.project_info.title;
            document.getElementById('project-description').textContent = data.project_info.description;
            document.getElementById('project-objective').textContent = data.project_info.objective;
            
            // Update summary counts
            document.getElementById('requirements-count').textContent = data.data_requirements.length;
            document.getElementById('rules-count').textContent = data.business_rules.length;
            document.getElementById('outputs-count').textContent = data.output_formats.length;
            
            // Populate requirements
            const requirementsContainer = document.getElementById('requirements-container');
            data.data_requirements.forEach(req => {
                const reqCard = document.createElement('div');
                reqCard.className = 'col-md-6 mb-4';
                reqCard.innerHTML = `
                    <div class="card requirement-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span>${req.name}</span>
                            <span class="badge bg-${req.required ? 'danger' : 'secondary'} badge-custom">
                                ${req.required ? 'Required' : 'Optional'}
                            </span>
                        </div>
                        <div class="card-body">
                            <p>${req.description}</p>
                            <div class="d-flex mb-3">
                                <span class="badge bg-primary badge-custom me-2">Type: ${req.type}</span>
                                <span class="badge bg-info badge-custom">Group: ${req.ui_settings.group}</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary toggle-details" data-id="${req.id}">
                                Show Details
                            </button>
                            <div class="requirement-details" id="details-${req.id}">
                                <h6 class="mb-3">Validation Rules</h6>
                                ${req.validation.rules.map(rule => `
                                    <div class="validation-rule">
                                        <i class="bi bi-check-circle me-2"></i> ${rule}
                                    </div>
                                `).join('') || '<p>No validation rules specified.</p>'}
                                
                                ${req.validation.options ? `
                                <h6 class="mt-3 mb-2">Options</h6>
                                <ul>
                                    ${req.validation.options.map(option => `<li>${option}</li>`).join('')}
                                </ul>
                                ` : ''}
                                
                                <h6 class="mt-3 mb-2">UI Settings</h6>
                                <p><strong>Group:</strong> ${req.ui_settings.group}</p>
                                <p><strong>Order:</strong> ${req.ui_settings.order}</p>
                                <p><strong>Width:</strong> ${req.ui_settings.width}</p>
                                <p><strong>Help Text:</strong> ${req.ui_settings.help_text}</p>
                            </div>
                        </div>
                    </div>
                `;
                requirementsContainer.appendChild(reqCard);
            });
            
            // Add event listeners to toggle requirement details
            document.querySelectorAll('.toggle-details').forEach(button => {
                button.addEventListener('click', function() {
                    const detailsId = this.getAttribute('data-id');
                    const detailsElement = document.getElementById(`details-${detailsId}`);
                    
                    if (detailsElement.style.display === 'block') {
                        detailsElement.style.display = 'none';
                        this.textContent = 'Show Details';
                    } else {
                        detailsElement.style.display = 'block';
                        this.textContent = 'Hide Details';
                    }
                });
            });
            
            // Populate business rules
            const businessRulesContainer = document.getElementById('business-rules-container');
            data.business_rules.forEach(rule => {
                const ruleElement = document.createElement('div');
                ruleElement.className = 'business-rule';
                ruleElement.innerHTML = `
                    <h5>${rule.description}</h5>
                    <p><strong>Condition:</strong> ${rule.condition}</p>
                    <p><strong>Action:</strong> ${rule.action}</p>
                    <div class="alert alert-${rule.condition_met ? 'success' : 'warning'}">
                        <strong>Status:</strong> ${rule.condition_met ? 'Condition Met' : 'Condition Not Met'}
                        <p class="mb-0 mt-1"><small>${rule.reason}</small></p>
                    </div>
                `;
                businessRulesContainer.appendChild(ruleElement);
            });
            
            // Populate output formats
            const outputFormatsContainer = document.getElementById('output-formats-container');
            data.output_formats.forEach(format => {
                const formatElement = document.createElement('div');
                formatElement.className = 'output-format';
                formatElement.innerHTML = `
                    <h5>${format.name}</h5>
                    <p>${format.description}</p>
                    <h6>Fields:</h6>
                    <ul>
                        ${format.fields.map(field => {
                            const reqField = data.data_requirements.find(req => req.id === field);
                            return `<li>${reqField ? reqField.name : field}</li>`;
                        }).join('')}
                    </ul>
                `;
                outputFormatsContainer.appendChild(formatElement);
            });
            
            // Populate validation checklist
            const validationChecklist = document.getElementById('validation-checklist');
            data.data_requirements.forEach(req => {
                const checklistItem = document.createElement('div');
                checklistItem.className = 'validation-item pending';
                checklistItem.innerHTML = `
                    <i class="bi bi-question-circle"></i>
                    <div>
                        <strong>${req.name}</strong>
                        <p class="mb-0 text-muted">${req.description}</p>
                    </div>
                `;
                validationChecklist.appendChild(checklistItem);
            });
            
            // Add validation button functionality
            document.getElementById('validate-btn').addEventListener('click', function() {
                // Simulate validation process
                const validationItems = document.querySelectorAll('.validation-item');
                let validCount = 0;
                
                validationItems.forEach((item, index) => {
                    // Simulate random validation results
                    const isValid = Math.random() > 0.3;
                    
                    setTimeout(() => {
                        item.className = `validation-item ${isValid ? 'valid' : 'invalid'}`;
                        item.querySelector('i').className = isValid ? 
                            'bi bi-check-circle' : 'bi bi-x-circle';
                        
                        if (isValid) validCount++;
                        
                        // Update progress
                        const progress = ((index + 1) / validationItems.length) * 100;
                        document.getElementById('validation-progress').style.width = `${progress}%`;
                        
                        // Update validation summary on dashboard
                        updateValidationSummary(validCount, validationItems.length);
                        
                        // Update final result if all items are processed
                        if (index === validationItems.length - 1) {
                            const validationResult = document.getElementById('validation-result');
                            if (validCount === validationItems.length) {
                                validationResult.className = 'alert alert-success';
                                validationResult.innerHTML = '<i class="bi bi-check-circle me-2"></i> All validation criteria met!';
                            } else {
                                validationResult.className = 'alert alert-danger';
                                validationResult.innerHTML = `<i class="bi bi-x-circle me-2"></i> ${validationItems.length - validCount} validation criteria failed.`;
                            }
                        }
                    }, index * 500); // Stagger the validation for visual effect
                });
            });
            
            // Initialize validation summary
            updateValidationSummary(0, data.data_requirements.length);
        }
        
        function updateValidationSummary(validCount, totalCount) {
            const validationSummary = document.getElementById('validation-summary');
            validationSummary.innerHTML = `
                <p><strong>${validCount}</strong> out of <strong>${totalCount}</strong> requirements validated</p>
                <div class="progress">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: ${(validCount / totalCount) * 100}%" 
                         aria-valuenow="${validCount}" aria-valuemin="0" aria-valuemax="${totalCount}">
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>

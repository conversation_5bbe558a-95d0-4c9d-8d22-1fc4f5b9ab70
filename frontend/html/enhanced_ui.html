<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Information Extractor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #4a86e8;
            --secondary-color: #34495e;
            --success-color: #66bb6a;
            --warning-color: #f5b041;
            --danger-color: #e57373;
            --light-color: #f5f5f5;
            --dark-color: #455a64;
            --text-color: #455a64;
            --border-color: #e0e0e0;
            --card-bg: #ffffff;
            --hover-bg: #f9f9f9;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
            min-height: 100vh;
        }

        .sidebar {
            background-color: white;
            color: var(--text-color);
            min-height: 100vh;
            padding: 20px 0;
            position: sticky;
            top: 0;
            border-right: 1px solid var(--border-color);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        .sidebar .nav-link {
            color: var(--text-color);
            padding: 10px 20px;
            margin: 5px 0;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .sidebar .nav-link:hover i, .sidebar .nav-link.active i {
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            transition: all 0.3s;
            background-color: var(--card-bg);
        }

        .card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        .card-header {
            background-color: #f8f9fa;
            color: var(--text-color);
            border-radius: 8px 8px 0 0 !important;
            padding: 15px 20px;
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
        }

        .card-header i {
            color: var(--primary-color);
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s;
            background-color: white;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--hover-bg);
        }

        .upload-area i {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .file-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .file-list li {
            background-color: var(--light-color);
            padding: 12px 15px;
            border-radius: 6px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border: 1px solid var(--border-color);
        }

        .file-list li:hover {
            background-color: var(--hover-bg);
        }

        .file-list li i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .file-list .remove-file {
            color: var(--text-color);
            cursor: pointer;
            font-size: 1.2rem;
            opacity: 0.6;
            transition: all 0.2s;
        }

        .file-list .remove-file:hover {
            opacity: 1;
            color: var(--danger-color);
        }

        .file-list .view-pdf-btn {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-color: var(--border-color);
            color: var(--text-color);
        }

        .file-list .view-pdf-btn:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .progress-container {
            margin-top: 20px;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: var(--light-color);
            overflow: hidden;
        }

        .progress-bar {
            background-color: var(--primary-color);
        }

        .status-message {
            margin-top: 10px;
            padding: 12px 15px;
            border-radius: 6px;
            background-color: var(--light-color);
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }

        .requirement-card {
            margin-bottom: 15px;
            border-left: 3px solid var(--primary-color);
        }

        .evidence-card {
            background-color: var(--light-color);
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
            border: 1px solid var(--border-color);
        }

        .pdf-viewer {
            width: 100%;
            height: 600px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: auto;
            position: relative;
            background-color: white;
        }

        .canvas-wrapper {
            display: inline-block;
            margin: 0 auto;
        }

        .canvas-wrapper canvas {
            display: block;
        }

        .key-point {
            padding: 12px 15px;
            border-radius: 6px;
            margin-bottom: 10px;
            background-color: var(--light-color);
            border-left: 3px solid var(--primary-color);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .key-point.addressed {
            border-left-color: var(--success-color);
        }

        .key-point.not-addressed {
            border-left-color: var(--danger-color);
        }

        .key-point i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .brd-stat-card {
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            background-color: white;
        }

        .brd-stat-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        .brd-stat-card .stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
            opacity: 0.8;
        }

        .brd-stat-card .stat-value {
            font-size: 1.75rem;
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--text-color);
        }

        .brd-stat-card .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .requirement-table {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .requirement-table th {
            background-color: #f8f9fa;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
        }

        .requirement-table tr:hover {
            background-color: var(--hover-bg);
        }

        .requirement-table .table-success {
            background-color: rgba(102, 187, 106, 0.1);
        }

        .requirement-table .table-danger {
            background-color: rgba(229, 115, 115, 0.1);
        }

        .extracted-info-card {
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
        }

        .extracted-info-card h5 {
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .extracted-info-card h5 i {
            color: var(--primary-color);
        }

        .extracted-info-content {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .evidence-section {
            margin-top: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
        }

        .evidence-header {
            background-color: #f8f9fa;
            color: var(--text-color);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .evidence-header h5 i {
            color: var(--primary-color);
        }

        .evidence-content {
            padding: 15px;
            background-color: white;
        }

        .clinical-analysis {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .clinical-analysis h6 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
        }

        .clinical-analysis .text-primary {
            color: var(--primary-color) !important;
        }

        .clinical-analysis .text-success {
            color: var(--success-color) !important;
        }

        .spinner-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            display: none;
        }

        .spinner-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 15px;
        }

        #pdf-tabs {
            margin-bottom: 15px;
        }

        #pdf-tabs .nav-link {
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }

        #pdf-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .tab-content {
            background-color: white;
            border-radius: 0 5px 5px 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="spinner-overlay" id="spinner-overlay">
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status"></div>
            <h5 id="spinner-message">Processing...</h5>
            <div class="progress mt-3" style="height: 10px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated" id="spinner-progress" role="progressbar" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="d-flex align-items-center mb-4 px-3">
                    <i class="bi bi-file-earmark-text fs-3 me-2"></i>
                    <h5 class="mb-0">PDF Extractor</h5>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#upload" data-bs-toggle="tab">
                            <i class="bi bi-cloud-upload"></i> Upload Files
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#brd-analysis" data-bs-toggle="tab" id="brd-tab">
                            <i class="bi bi-list-check"></i> BRD Analysis
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#extraction-results" data-bs-toggle="tab" id="results-tab">
                            <i class="bi bi-file-earmark-text"></i> Extraction Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pdf-viewer" data-bs-toggle="tab" id="pdf-viewer-tab">
                            <i class="bi bi-file-earmark-pdf"></i> PDF Viewer
                        </a>
                    </li>
                </ul>

                <div class="mt-5 px-3">
                    <h6 class="text-uppercase text-muted mb-3" style="font-size: 0.8rem;">Instructions</h6>
                    <div class="small text-light">
                        <p>1. Upload a BRD file (DOC/DOCX)</p>
                        <p>2. Upload one or more PDF files</p>
                        <p>3. Click "Start Extraction" to begin</p>
                        <p>4. View the BRD analysis and extraction results</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="tab-content">
                    <!-- Upload Tab -->
                    <div class="tab-pane fade show active" id="upload">
                        <h2 class="mb-4">Upload Files</h2>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <i class="bi bi-file-earmark-word me-2"></i> BRD Document
                                    </div>
                                    <div class="card-body">
                                        <div class="upload-area" id="brd-upload-area">
                                            <i class="bi bi-file-earmark-word"></i>
                                            <h5>Upload BRD File</h5>
                                            <p class="text-muted">Drag & drop a DOC or DOCX file here, or click to browse</p>
                                            <input type="file" id="brd-file-input" accept=".doc,.docx" style="display: none;">
                                            <button class="btn btn-primary" id="brd-browse-btn">Browse Files</button>
                                        </div>

                                        <ul class="file-list" id="brd-file-list">
                                            <!-- BRD files will be listed here -->
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <i class="bi bi-file-earmark-pdf me-2"></i> PDF & PKL Documents
                                    </div>
                                    <div class="card-body">
                                        <div class="upload-area" id="pdf-upload-area">
                                            <i class="bi bi-file-earmark-pdf"></i>
                                            <h5>Upload PDF or PKL Files</h5>
                                            <p class="text-muted">Drag & drop PDF or PKL files here, or click to browse</p>
                                            <input type="file" id="pdf-file-input" accept=".pdf,.pkl" multiple style="display: none;">
                                            <button class="btn btn-primary" id="pdf-browse-btn">Browse Files</button>
                                        </div>

                                        <ul class="file-list" id="pdf-file-list">
                                            <!-- PDF files will be listed here -->
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button class="btn btn-lg btn-primary" id="start-extraction-btn" disabled>
                                <i class="bi bi-play-fill me-2"></i> Start Extraction
                            </button>
                        </div>

                        <div class="progress-container" id="progress-container" style="display: none;">
                            <h5>Extraction Progress</h5>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" id="extraction-progress" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="status-message" id="status-message">
                                Initializing extraction process...
                            </div>
                        </div>
                    </div>

                    <!-- BRD Analysis Tab -->
                    <div class="tab-pane fade" id="brd-analysis">
                        <h2 class="mb-4">BRD Analysis</h2>
                        <div id="brd-analysis-content">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i> Please upload a BRD file and start the extraction process to view the analysis.
                            </div>
                        </div>
                    </div>

                    <!-- Extraction Results Tab -->
                    <div class="tab-pane fade" id="extraction-results">
                        <h2 class="mb-4">Extraction Results</h2>
                        <div id="extraction-results-content">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i> Please upload files and start the extraction process to view the results.
                            </div>
                        </div>

                        <div class="text-center mt-4" style="display: none;" id="download-json-container">
                            <button class="btn btn-success" id="download-json-btn">
                                <i class="bi bi-download me-2"></i> Download Results (JSON)
                            </button>
                        </div>
                    </div>

                    <!-- PDF Viewer Tab -->
                    <div class="tab-pane fade" id="pdf-viewer">
                        <h2 class="mb-4">PDF Viewer</h2>
                        <div id="pdf-viewer-content">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i> Please upload PDF files to view them here.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- PDF.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
    <script>
        // Set the worker source
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';
    </script>
    <script src="enhanced_ui.js"></script>
</body>
</html>

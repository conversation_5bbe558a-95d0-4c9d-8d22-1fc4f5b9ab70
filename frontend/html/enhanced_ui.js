// Configuration
const API_URL = "http://localhost:8001"; // FastAPI backend URL

// Global variables
let brdFile = null;
let pdfFiles = [];
let processId = null;
let pollingInterval = null;
let extractionResults = null;
let pdfViewers = {};
let processCompleted = false;

// DOM elements
document.addEventListener('DOMContentLoaded', function() {
    // BRD file upload
    const brdUploadArea = document.getElementById('brd-upload-area');
    const brdFileInput = document.getElementById('brd-file-input');
    const brdBrowseBtn = document.getElementById('brd-browse-btn');
    const brdFileList = document.getElementById('brd-file-list');

    // PDF files upload
    const pdfUploadArea = document.getElementById('pdf-upload-area');
    const pdfFileInput = document.getElementById('pdf-file-input');
    const pdfBrowseBtn = document.getElementById('pdf-browse-btn');
    const pdfFileList = document.getElementById('pdf-file-list');

    // Extraction controls
    const startExtractionBtn = document.getElementById('start-extraction-btn');
    const progressContainer = document.getElementById('progress-container');
    const extractionProgress = document.getElementById('extraction-progress');
    const statusMessage = document.getElementById('status-message');

    // Tab navigation
    const brdTab = document.getElementById('brd-tab');
    const resultsTab = document.getElementById('results-tab');
    const pdfViewerTab = document.getElementById('pdf-viewer-tab');

    // Content containers
    const brdAnalysisContent = document.getElementById('brd-analysis-content');
    const extractionResultsContent = document.getElementById('extraction-results-content');
    const pdfViewerContent = document.getElementById('pdf-viewer-content');
    const downloadJsonContainer = document.getElementById('download-json-container');
    const downloadJsonBtn = document.getElementById('download-json-btn');

    // Loading spinner
    const spinnerOverlay = document.getElementById('spinner-overlay');
    const spinnerMessage = document.getElementById('spinner-message');
    const spinnerProgress = document.getElementById('spinner-progress');

    // Initialize event listeners
    initializeEventListeners();

    function initializeEventListeners() {
        // BRD file upload
        brdBrowseBtn.addEventListener('click', () => brdFileInput.click());
        brdFileInput.addEventListener('change', handleBrdFileSelect);
        brdUploadArea.addEventListener('dragover', handleDragOver);
        brdUploadArea.addEventListener('drop', handleBrdFileDrop);

        // PDF files upload
        pdfBrowseBtn.addEventListener('click', () => pdfFileInput.click());
        pdfFileInput.addEventListener('change', handlePdfFileSelect);
        pdfUploadArea.addEventListener('dragover', handleDragOver);
        pdfUploadArea.addEventListener('drop', handlePdfFileDrop);

        // Start extraction
        startExtractionBtn.addEventListener('click', startExtraction);

        // Download JSON
        downloadJsonBtn.addEventListener('click', downloadJson);

        // PDF viewer tab click - show uploaded PDFs when tab is clicked
        pdfViewerTab.addEventListener('click', function() {
            // If we don't have extraction results yet but have uploaded PDFs, display them
            if (!extractionResults && pdfFiles.length > 0) {
                displayLocalPdfFiles(pdfFiles);
            }
        });
    }

    // File handling functions
    function handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('border-primary');
    }

    function handleBrdFileDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('border-primary');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (isValidBrdFile(file)) {
                setBrdFile(file);
            } else {
                showAlert('Please upload a valid BRD file (.doc or .docx)', 'danger');
            }
        }
    }

    function handlePdfFileDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('border-primary');

        const files = e.dataTransfer.files;
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (isValidPdfFile(file)) {
                addPdfFile(file);
            } else {
                showAlert('Please upload valid PDF or PKL files', 'danger');
            }
        }
    }

    function handleBrdFileSelect(e) {
        const file = e.target.files[0];
        if (file && isValidBrdFile(file)) {
            setBrdFile(file);
        }
    }

    function handlePdfFileSelect(e) {
        const files = e.target.files;
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (isValidPdfFile(file)) {
                addPdfFile(file);
            }
        }
    }

    function isValidBrdFile(file) {
        const validExtensions = ['.doc', '.docx'];
        const fileName = file.name.toLowerCase();
        return validExtensions.some(ext => fileName.endsWith(ext));
    }

    function isValidPdfFile(file) {
        const fileName = file.name.toLowerCase();
        return fileName.endsWith('.pdf') || fileName.endsWith('.pkl');
    }

    function setBrdFile(file) {
        brdFile = file;
        brdFileList.innerHTML = '';

        const li = document.createElement('li');
        li.innerHTML = `
            <div>
                <i class="bi bi-file-earmark-word"></i>
                <span>${file.name}</span>
            </div>
            <i class="bi bi-x-circle remove-file" data-type="brd"></i>
        `;
        brdFileList.appendChild(li);

        li.querySelector('.remove-file').addEventListener('click', () => {
            brdFile = null;
            brdFileList.innerHTML = '';
            updateStartButton();
        });

        updateStartButton();
    }

    function addPdfFile(file) {
        // Check if file already exists
        if (pdfFiles.some(f => f.name === file.name)) {
            return;
        }

        pdfFiles.push(file);

        const isPkl = file.name.toLowerCase().endsWith('.pkl');
        const fileIcon = isPkl ? 'bi-file-earmark-binary' : 'bi-file-earmark-pdf';

        const li = document.createElement('li');
        li.innerHTML = `
            <div>
                <i class="bi ${fileIcon}"></i>
                <span>${file.name}</span>
            </div>
            <div>
                <button class="btn btn-sm btn-outline-primary view-pdf-btn me-2" data-filename="${file.name}" ${isPkl ? 'style="display:none"' : ''}>
                    <i class="bi bi-eye"></i> View
                </button>
                <i class="bi bi-x-circle remove-file" data-type="pdf" data-name="${file.name}"></i>
            </div>
        `;
        pdfFileList.appendChild(li);

        // Add event listener for the view button
        li.querySelector('.view-pdf-btn').addEventListener('click', function() {
            // Switch to PDF viewer tab
            const pdfViewerTabEl = new bootstrap.Tab(pdfViewerTab);
            pdfViewerTabEl.show();

            // Display the PDFs
            displayLocalPdfFiles(pdfFiles);
        });

        li.querySelector('.remove-file').addEventListener('click', function() {
            const fileName = this.getAttribute('data-name');
            pdfFiles = pdfFiles.filter(f => f.name !== fileName);
            li.remove();
            updateStartButton();

            // If we're on the PDF viewer tab, update the display
            if (pdfViewerTab.classList.contains('active')) {
                if (pdfFiles.length > 0) {
                    displayLocalPdfFiles(pdfFiles);
                } else {
                    pdfViewerContent.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i> No PDF files available for viewing.
                        </div>
                    `;
                }
            }
        });

        updateStartButton();
    }

    function updateStartButton() {
        startExtractionBtn.disabled = !(brdFile && pdfFiles.length > 0);
    }

    // Extraction process
    async function startExtraction() {
        try {
            // Reset the process completed flag
            processCompleted = false;

            showSpinner('Preparing files for upload...');

            // Create FormData
            const formData = new FormData();
            formData.append('brd_file', brdFile);

            // Add all PDF files
            pdfFiles.forEach(file => {
                formData.append('pdf_files', file);
            });

            // Upload files and start extraction
            const response = await fetch(`${API_URL}/upload-and-process/`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status} - ${await response.text()}`);
            }

            const data = await response.json();
            processId = data.process_id;

            // Show progress container
            progressContainer.style.display = 'block';

            // Start polling for status
            startPolling();

        } catch (error) {
            console.error('Error starting extraction:', error);
            showAlert(`Failed to start extraction: ${error.message}`, 'danger');
            hideSpinner();
        }
    }

    function startPolling() {
        // Reset the process completed flag
        processCompleted = false;

        // Clear any existing interval
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }

        // Poll every 2 seconds
        pollingInterval = setInterval(pollStatus, 2000);
    }

    async function pollStatus() {
        // If the process is already completed, stop polling
        if (processCompleted) {
            console.log("Process already completed, stopping polling");
            clearInterval(pollingInterval);
            return;
        }

        try {
            const response = await fetch(`${API_URL}/status/${processId}`);

            if (!response.ok) {
                throw new Error(`API Error: ${response.status} - ${await response.text()}`);
            }

            const data = await response.json();
            updateStatus(data);

            // If processing is complete or failed, stop polling
            if (data.status === 'completed' || data.status === 'failed') {
                // Set the process completed flag
                processCompleted = true;
                clearInterval(pollingInterval);

                if (data.status === 'completed') {
                    extractionResults = data.result;
                    displayResults(extractionResults);
                    hideSpinner();
                } else {
                    showAlert(`Extraction failed: ${data.error || 'Unknown error'}`, 'danger');
                    hideSpinner();
                }
            }
        } catch (error) {
            console.error('Error polling status:', error);
            showAlert(`Failed to get status: ${error.message}`, 'danger');
            processCompleted = true;
            clearInterval(pollingInterval);
            hideSpinner();
        }
    }

    function updateStatus(data) {
        const status = data.status;
        const message = data.message;
        const progress = data.progress || 0;

        // Update progress bar
        extractionProgress.style.width = `${progress}%`;
        extractionProgress.setAttribute('aria-valuenow', progress);

        // Update status message
        statusMessage.textContent = message;

        // Update spinner
        updateSpinner(message, progress);

        // Add status-specific styling
        statusMessage.className = 'status-message';
        if (status === 'processing') {
            statusMessage.classList.add('bg-info', 'text-white');
        } else if (status === 'completed') {
            statusMessage.classList.add('bg-success', 'text-white');
        } else if (status === 'failed') {
            statusMessage.classList.add('bg-danger', 'text-white');
        }
    }

    // Results display
    function displayResults(results) {
        // Display BRD analysis
        displayBrdAnalysis(results);

        // Display extraction results
        displayExtractionResults(results);

        // Display PDF viewer
        displayPdfViewer(results);

        // Show download button
        downloadJsonContainer.style.display = 'block';

        // Switch to results tab
        const resultsTabEl = new bootstrap.Tab(resultsTab);
        resultsTabEl.show();
    }

    function displayBrdAnalysis(results) {
        const extractedData = results.extracted_data;

        if (!extractedData || extractedData.length === 0) {
            brdAnalysisContent.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i> No data was extracted from the BRD.
                </div>
            `;
            return;
        }

        // Check if we're using the consolidated requirement approach
        const isConsolidated = extractedData.length === 1 && "key_points" in extractedData[0];

        if (isConsolidated) {
            const item = extractedData[0];
            const keyPoints = item.key_points || [];
            const addressedPoints = item.addressed_points || [];

            // Calculate percentage of key points addressed
            const addressedCount = addressedPoints.length;
            const totalPoints = keyPoints.length;
            const percentage = totalPoints > 0 ? (addressedCount / totalPoints) * 100 : 0;

            let html = `
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-info-circle me-2"></i> Project Information
                        </div>
                        <div>
                            <span class="badge ${item.found ? 'bg-light text-success border border-success' : 'bg-light text-danger border border-danger'} p-2">
                                <i class="bi ${item.found ? 'bi-check-circle-fill' : 'bi-x-circle-fill'} me-1"></i>
                                ${item.found ? 'Information Found' : 'Information Not Found'}
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h4 class="mb-3">${item.requirement_text}</h4>
                        <p class="text-muted">${item.context || ''}</p>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-list-check me-2"></i> BRD Analysis Summary
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="brd-stat-card bg-white">
                                    <div class="stat-icon text-${item.found ? 'success' : 'danger'}">
                                        <i class="bi ${item.found ? 'bi-check-circle-fill' : 'bi-x-circle-fill'}"></i>
                                    </div>
                                    <div class="stat-value text-${item.found ? 'success' : 'danger'}">${item.found ? 'Yes' : 'No'}</div>
                                    <div class="stat-label">Information Found</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="brd-stat-card bg-white">
                                    <div class="stat-icon text-primary">
                                        <i class="bi bi-list-check"></i>
                                    </div>
                                    <div class="stat-value">${totalPoints}</div>
                                    <div class="stat-label">Total Requirements</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="brd-stat-card bg-white">
                                    <div class="stat-icon text-success">
                                        <i class="bi bi-check-all"></i>
                                    </div>
                                    <div class="stat-value">${addressedCount}</div>
                                    <div class="stat-label">Requirements Addressed</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="brd-stat-card bg-white">
                                    <div class="stat-icon text-info">
                                        <i class="bi bi-pie-chart-fill"></i>
                                    </div>
                                    <div class="stat-value">${percentage.toFixed(1)}%</div>
                                    <div class="stat-label">Coverage</div>
                                </div>
                            </div>
                        </div>

                        <div class="progress mb-4" style="height: 20px; border-radius: 8px; overflow: hidden;">
                            <div class="progress-bar" role="progressbar" style="width: ${percentage}%"
                                 aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                                ${percentage.toFixed(1)}% Complete
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">BRD Requirements</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover requirement-table">
                                <thead>
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="75%">Requirement</th>
                                        <th width="20%">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            keyPoints.forEach((point, index) => {
                const isAddressed = addressedPoints.includes(point);
                const rowId = `req-row-${index}`;

                // Get the reasoning for this point if available
                const reasoningData = item.reasoning && item.reasoning[point] ? item.reasoning[point] : '';

                // Check if we have the new structure or the old string format
                let hasReasoning = false;
                let reasoningText = '';
                let evidenceList = [];

                if (reasoningData) {
                    if (typeof reasoningData === 'object') {
                        // New structure with reasoning_text and evidence
                        reasoningText = reasoningData.reasoning_text || '';
                        evidenceList = reasoningData.evidence || [];
                        hasReasoning = reasoningText || evidenceList.length > 0;
                    } else {
                        // Old string format
                        reasoningText = reasoningData;
                        hasReasoning = reasoningText.length > 0;
                    }
                }

                html += `
                    <tr id="${rowId}" class="${isAddressed ? 'table-success' : 'table-danger'}"
                        data-bs-toggle="collapse" data-bs-target="#collapse-${rowId}"
                        aria-expanded="false" aria-controls="collapse-${rowId}"
                        style="cursor: pointer;">
                        <td>${index + 1}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi ${isAddressed ? 'bi-check-circle-fill text-success' : 'bi-x-circle-fill text-danger'} me-2"></i>
                                ${point}
                                ${hasReasoning ? '<i class="bi bi-info-circle ms-2 text-primary"></i>' : ''}
                                ${evidenceList && evidenceList.length > 0 ? `<span class="badge bg-light text-primary border border-primary ms-2">${evidenceList.length} evidence</span>` : ''}
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="badge ${isAddressed ? 'bg-light text-success border border-success' : 'bg-light text-danger border border-danger'} p-2">
                                ${isAddressed ? 'Addressed' : 'Not Addressed'}
                            </span>
                        </td>
                    </tr>
                    ${hasReasoning ? `
                    <tr class="collapse" id="collapse-${rowId}">
                        <td colspan="3" class="p-0">
                            <div class="p-3 bg-light">
                                <h6 class="mb-2 text-primary"><i class="bi bi-info-circle me-2"></i>Clinical Analysis:</h6>
                                <div class="clinical-analysis mb-0">
                                    ${formatReasoningText(reasoningText)}
                                </div>

                                ${evidenceList && evidenceList.length > 0 ? `
                                <div class="mt-4">
                                    <h6 class="mb-2 text-primary"><i class="bi bi-file-text me-2"></i>Supporting Evidence (${evidenceList.length}):</h6>
                                    <div class="accordion" id="evidenceAccordion-${rowId}">
                                        ${evidenceList.map((evidence, evidenceIndex) => `
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="evidenceHeading-${rowId}-${evidenceIndex}">
                                                    <button class="accordion-button ${evidenceIndex === 0 ? '' : 'collapsed'}" type="button"
                                                            data-bs-toggle="collapse" data-bs-target="#evidenceCollapse-${rowId}-${evidenceIndex}"
                                                            aria-expanded="${evidenceIndex === 0 ? 'true' : 'false'}"
                                                            aria-controls="evidenceCollapse-${rowId}-${evidenceIndex}">
                                                        <div class="d-flex align-items-center w-100">
                                                            <span class="badge bg-light text-primary border border-primary me-2">
                                                                Page ${evidence.page_number !== 'UNKNOWN' ? evidence.page_number : ''}
                                                            </span>
                                                            <span class="text-truncate">${evidence.text}</span>
                                                        </div>
                                                    </button>
                                                </h2>
                                                <div id="evidenceCollapse-${rowId}-${evidenceIndex}"
                                                     class="accordion-collapse collapse ${evidenceIndex === 0 ? 'show' : ''}"
                                                     aria-labelledby="evidenceHeading-${rowId}-${evidenceIndex}"
                                                     data-bs-parent="#evidenceAccordion-${rowId}">
                                                    <div class="accordion-body">
                                                        <div class="d-flex justify-content-end mb-2">
                                                            <button class="btn btn-sm btn-outline-primary view-in-pdf-btn"
                                                                    data-filename="${evidence.source_filename}"
                                                                    data-page="${evidence.page_number || 1}">
                                                                <i class="bi bi-eye me-1"></i> View in PDF
                                                            </button>
                                                        </div>
                                                        <div class="p-3 border rounded bg-light">
                                                            <p class="fst-italic mb-0">${evidence.text}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                    ` : ''}
                `;
            });

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            brdAnalysisContent.innerHTML = html;
        } else {
            // For multiple requirements (original approach)
            const totalRequirements = extractedData.length;
            const foundRequirements = extractedData.filter(item => item.found).length;
            const percentage = totalRequirements > 0 ? (foundRequirements / totalRequirements) * 100 : 0;

            let html = `
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-list-check me-2"></i> BRD Analysis Summary
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="brd-stat-card bg-white">
                                    <div class="stat-icon text-primary">
                                        <i class="bi bi-list-check"></i>
                                    </div>
                                    <div class="stat-value">${totalRequirements}</div>
                                    <div class="stat-label">Total Requirements</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="brd-stat-card bg-white">
                                    <div class="stat-icon text-success">
                                        <i class="bi bi-check-all"></i>
                                    </div>
                                    <div class="stat-value">${foundRequirements}</div>
                                    <div class="stat-label">Requirements Found</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="brd-stat-card bg-white">
                                    <div class="stat-icon text-info">
                                        <i class="bi bi-pie-chart-fill"></i>
                                    </div>
                                    <div class="stat-value">${percentage.toFixed(1)}%</div>
                                    <div class="stat-label">Coverage</div>
                                </div>
                            </div>
                        </div>

                        <div class="progress mb-4" style="height: 20px; border-radius: 8px; overflow: hidden;">
                            <div class="progress-bar" role="progressbar" style="width: ${percentage}%"
                                 aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                                ${percentage.toFixed(1)}% Complete
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">BRD Requirements</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover requirement-table">
                                <thead>
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="75%">Requirement</th>
                                        <th width="20%">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            extractedData.forEach((item, index) => {
                const rowId = `req-row-legacy-${index}`;
                html += `
                    <tr id="${rowId}" class="${item.found ? 'table-success' : 'table-danger'}"
                        data-bs-toggle="collapse" data-bs-target="#collapse-${rowId}"
                        aria-expanded="false" aria-controls="collapse-${rowId}"
                        style="cursor: pointer;">
                        <td>${index + 1}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi ${item.found ? 'bi-check-circle-fill text-success' : 'bi-x-circle-fill text-danger'} me-2"></i>
                                ${item.requirement_text}
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="badge ${item.found ? 'bg-light text-success border border-success' : 'bg-light text-danger border border-danger'} p-2">
                                ${item.found ? 'Found' : 'Not Found'}
                            </span>
                        </td>
                    </tr>
                    ${item.notes ? `
                    <tr class="collapse" id="collapse-${rowId}">
                        <td colspan="3" class="p-0">
                            <div class="p-3 bg-light">
                                <h6 class="mb-2 text-primary"><i class="bi bi-info-circle me-2"></i>Notes:</h6>
                                <p class="mb-0">${item.notes}</p>
                            </div>
                        </td>
                    </tr>
                    ` : ''}
                `;
            });

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            brdAnalysisContent.innerHTML = html;
        }
    }

    function displayExtractionResults(results) {
        const extractedData = results.extracted_data;

        if (!extractedData || extractedData.length === 0) {
            extractionResultsContent.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i> No data was extracted.
                </div>
            `;
            return;
        }

        let html = `
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-file-earmark-text me-2"></i> Extraction Results Summary
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Found information for ${extractedData.filter(item => item.found).length} out of ${extractedData.length} requirements.
                    </div>
                </div>
            </div>
        `;

        extractedData.forEach((item, index) => {
            html += `
                <div class="card mb-4 requirement-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><strong>${item.requirement_id || `Requirement ${index + 1}`}</strong></span>
                        <span class="badge ${item.found ? 'bg-light text-success border border-success' : 'bg-light text-danger border border-danger'} p-2">
                            <i class="bi ${item.found ? 'bi-check-circle-fill' : 'bi-x-circle-fill'} me-1"></i>
                            ${item.found ? 'Found' : 'Not Found'}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-12">
                                <h5 class="border-bottom pb-2">Requirement</h5>
                                <p>${item.requirement_text}</p>
                            </div>
                        </div>

                        ${item.found ? `
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="extracted-info-card">
                                        <h5><i class="bi bi-card-text me-2"></i>Extracted Information</h5>
                                        <div class="extracted-info-content">
                                            ${formatExtractedInfo(item.extracted_info) || 'No specific information extracted.'}
                                        </div>
                                    </div>
                                </div>
                            </div>


                            ${item.all_supporting_evidence && item.all_supporting_evidence.length > 1 ? `
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>All Supporting Evidence</h5>
                                            </div>
                                            <div class="card-body p-0">
                                                ${(() => {
                                                    // Use grouped_evidence if available, otherwise group it ourselves
                                                    let evidenceByField = {};

                                                    if (item.grouped_evidence) {
                                                        // Use the pre-grouped evidence from the backend
                                                        evidenceByField = item.grouped_evidence;
                                                    } else {
                                                        // Group evidence by field_id (for backward compatibility)
                                                        item.all_supporting_evidence.forEach(evidence => {
                                                            const fieldId = evidence.field_id || 'Unknown field';
                                                            if (!evidenceByField[fieldId]) {
                                                                evidenceByField[fieldId] = [];
                                                            }
                                                            evidenceByField[fieldId].push(evidence);
                                                        });
                                                    }

                                                    // Generate HTML for each field group
                                                    let fieldGroupsHtml = '';
                                                    let fieldIndex = 0;

                                                    // Sort field IDs alphabetically for consistency
                                                    const sortedFieldIds = Object.keys(evidenceByField).sort();

                                                    for (const fieldId of sortedFieldIds) {
                                                        const evidenceList = evidenceByField[fieldId];
                                                        fieldGroupsHtml += `
                                                            <div class="accordion-item">
                                                                <h2 class="accordion-header" id="fieldHeading-${index}-${fieldIndex}">
                                                                    <button class="accordion-button ${fieldIndex === 0 ? '' : 'collapsed'}" type="button"
                                                                            data-bs-toggle="collapse" data-bs-target="#fieldCollapse-${index}-${fieldIndex}"
                                                                            aria-expanded="${fieldIndex === 0 ? 'true' : 'false'}"
                                                                            aria-controls="fieldCollapse-${index}-${fieldIndex}">
                                                                        <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                                            <span>Evidence for: ${fieldId}</span>
                                                                            <span class="badge bg-light text-primary border border-primary">
                                                                                ${evidenceList.length} items
                                                                            </span>
                                                                        </div>
                                                                        <div class="small text-muted d-none d-md-block ms-2">
                                                                            Similar evidences filtered out, sorted by confidence
                                                                        </div>
                                                                    </button>
                                                                </h2>
                                                                <div id="fieldCollapse-${index}-${fieldIndex}"
                                                                     class="accordion-collapse collapse ${fieldIndex === 0 ? 'show' : ''}"
                                                                     aria-labelledby="fieldHeading-${index}-${fieldIndex}"
                                                                     data-bs-parent="#evidenceAccordion-${index}">
                                                                    <div class="accordion-body">
                                                                        <div class="accordion" id="evidenceSubAccordion-${index}-${fieldIndex}">
                                                                            ${evidenceList.map((evidence, evidenceIndex) => `
                                                                                <div class="accordion-item">
                                                                                    <h2 class="accordion-header" id="evidenceHeading-${index}-${fieldIndex}-${evidenceIndex}">
                                                                                        <button class="accordion-button ${evidenceIndex === 0 ? '' : 'collapsed'}" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#evidenceCollapse-${index}-${fieldIndex}-${evidenceIndex}"
                                                                                                aria-expanded="${evidenceIndex === 0 ? 'true' : 'false'}"
                                                                                                aria-controls="evidenceCollapse-${index}-${fieldIndex}-${evidenceIndex}">
                                                                                            <div class="d-flex align-items-center w-100">
                                                                                                <span class="badge bg-light text-primary border border-primary me-2">
                                                                                                    Page ${evidence.page_number !== 'UNKNOWN' ? evidence.page_number : ''}
                                                                                                </span>
                                                                                                <span class="text-truncate">${evidence.text}</span>
                                                                                            </div>
                                                                                        </button>
                                                                                    </h2>
                                                                                    <div id="evidenceCollapse-${index}-${fieldIndex}-${evidenceIndex}"
                                                                                         class="accordion-collapse collapse ${evidenceIndex === 0 ? 'show' : ''}"
                                                                                         aria-labelledby="evidenceHeading-${index}-${fieldIndex}-${evidenceIndex}"
                                                                                         data-bs-parent="#evidenceSubAccordion-${index}-${fieldIndex}">
                                                                                        <div class="accordion-body">
                                                                                            <div class="d-flex justify-content-end mb-2">
                                                                                                <button class="btn btn-sm btn-outline-primary view-pdf-btn"
                                                                                                        data-filename="${evidence.source_filename}"
                                                                                                        data-page="${evidence.page_number || 1}">
                                                                                                    <i class="bi bi-eye me-1"></i> View in PDF
                                                                                                </button>
                                                                                            </div>
                                                                                            <div class="p-3 border rounded bg-light">
                                                                                                <p class="fst-italic mb-0">${evidence.text}</p>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            `).join('')}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        `;
                                                        fieldIndex++;
                                                    }

                                                    return `<div class="accordion" id="evidenceAccordion-${index}">${fieldGroupsHtml}</div>`;
                                                })()}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                        ` : `
                            <div class="alert alert-warning mt-3">
                                <i class="bi bi-exclamation-triangle me-2"></i> ${item.notes || 'No relevant information found.'}
                            </div>
                        `}

                        ${item.notes && item.found ? `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i> ${item.notes}
                                    </div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        extractionResultsContent.innerHTML = html;

        // Add event listeners for "View in PDF" buttons
        document.querySelectorAll('.view-pdf-btn').forEach(button => {
            button.addEventListener('click', function() {
                const filename = this.getAttribute('data-filename');
                const page = parseInt(this.getAttribute('data-page'), 10);

                // Switch to PDF viewer tab
                const pdfViewerTabEl = new bootstrap.Tab(pdfViewerTab);
                pdfViewerTabEl.show();

                // Find the PDF tab with matching filename and click it
                const pdfTabs = document.querySelectorAll('#pdf-tabs .nav-link');
                let found = false;

                // First try to find an exact match
                for (let i = 0; i < pdfTabs.length; i++) {
                    if (pdfTabs[i].textContent.trim() === filename) {
                        pdfTabs[i].click();
                        found = true;

                        // Navigate to the specific page if possible
                        const tabId = pdfTabs[i].getAttribute('id');
                        const contentId = pdfTabs[i].getAttribute('data-bs-target').substring(1);
                        const viewerId = contentId.replace('pdf-content-', 'pdf-viewer-');

                        if (pdfViewers[viewerId] && page > 0 && page <= pdfViewers[viewerId].totalPages) {
                            // Update page number display
                            const pageDisplay = document.querySelector(`#${contentId} .current-page`);
                            if (pageDisplay) {
                                pageDisplay.textContent = page;
                            }

                            // Render the specific page
                            pdfViewers[viewerId].currentPage = page;
                            renderPage(viewerId, page);
                        }

                        break;
                    }
                }

                // If no exact match, try to find a tab that contains the filename
                // (might be prefixed with process_id)
                if (!found) {
                    for (let i = 0; i < pdfTabs.length; i++) {
                        const tabText = pdfTabs[i].textContent.trim();
                        // Check if the tab text ends with the filename (to handle process_id prefix)
                        if (tabText.endsWith(filename) || filename.endsWith(tabText)) {
                            pdfTabs[i].click();

                            // Navigate to the specific page if possible
                            const tabId = pdfTabs[i].getAttribute('id');
                            const contentId = pdfTabs[i].getAttribute('data-bs-target').substring(1);
                            const viewerId = contentId.replace('pdf-content-', 'pdf-viewer-');

                            if (pdfViewers[viewerId] && page > 0 && page <= pdfViewers[viewerId].totalPages) {
                                // Update page number display
                                const pageDisplay = document.querySelector(`#${contentId} .current-page`);
                                if (pageDisplay) {
                                    pageDisplay.textContent = page;
                                }

                                // Render the specific page
                                pdfViewers[viewerId].currentPage = page;
                                renderPage(viewerId, page);
                            }

                            break;
                        }
                    }
                }
            });
        });
    }

    function displayPdfViewer(results) {
        // If we have results with PDF files from the server, use those
        if (results && results.pdf_files && results.pdf_files.length > 0) {
            displayServerPdfFiles(results.pdf_files);
            return;
        }

        // If we have locally uploaded PDF files but no server results yet, display those
        if (pdfFiles && pdfFiles.length > 0) {
            displayLocalPdfFiles(pdfFiles);
            return;
        }

        // If no PDFs are available at all
        pdfViewerContent.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i> No PDF files available for viewing.
            </div>
        `;
    }

    function displayServerPdfFiles(pdfFiles) {

        // Create tabs for each PDF
        let tabsHtml = '<ul class="nav nav-tabs" id="pdf-tabs" role="tablist">';
        let tabContentHtml = '<div class="tab-content" id="pdf-tabs-content">';

        pdfFiles.forEach((pdf, index) => {
            const tabId = `pdf-tab-${index}`;
            const contentId = `pdf-content-${index}`;
            const isActive = index === 0;

            tabsHtml += `
                <li class="nav-item" role="presentation">
                    <button class="nav-link ${isActive ? 'active' : ''}" id="${tabId}" data-bs-toggle="tab"
                            data-bs-target="#${contentId}" type="button" role="tab"
                            aria-controls="${contentId}" aria-selected="${isActive}">
                        ${pdf.original_filename}
                    </button>
                </li>
            `;

            tabContentHtml += `
                <div class="tab-pane fade ${isActive ? 'show active' : ''}" id="${contentId}" role="tabpanel" aria-labelledby="${tabId}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h5>${pdf.original_filename}</h5>
                        </div>
                        <div>
                            <a href="${pdf.url_path ? `${API_URL}${pdf.url_path}` : `${API_URL}/download/${pdf.original_filename}`}"
                               class="btn btn-sm btn-outline-primary"
                               target="_blank"
                               download="${pdf.original_filename}">
                                <i class="bi bi-download me-1"></i> Download
                            </a>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="pdf-viewer" id="pdf-viewer-${index}">
                                <div class="text-center p-5">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">Loading PDF...</p>
                                </div>
                            </div>
                            <div class="pdf-navigation d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <button class="btn btn-sm btn-outline-secondary prev-page" data-viewer-id="pdf-viewer-${index}">
                                        <i class="bi bi-chevron-left"></i> Previous
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary next-page ms-2" data-viewer-id="pdf-viewer-${index}">
                                        <i class="bi bi-chevron-right"></i> Next
                                    </button>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <button class="btn btn-sm btn-outline-secondary zoom-out" data-viewer-id="pdf-viewer-${index}">
                                            <i class="bi bi-dash-lg"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary zoom-in ms-1" data-viewer-id="pdf-viewer-${index}">
                                            <i class="bi bi-plus-lg"></i>
                                        </button>
                                    </div>
                                    <div>
                                        Page <span class="current-page">1</span> of <span class="total-pages">...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        tabsHtml += '</ul>';
        tabContentHtml += '</div>';

        pdfViewerContent.innerHTML = tabsHtml + tabContentHtml;

        // Add event listeners for navigation buttons
        document.querySelectorAll('.prev-page').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer && viewer.currentPage > 1) {
                    viewer.currentPage--;
                    renderPage(viewerId, viewer.currentPage);

                    // Update page number display
                    const container = document.getElementById(viewerId).closest('.tab-pane');
                    const pageDisplay = container.querySelector('.current-page');
                    if (pageDisplay) {
                        pageDisplay.textContent = viewer.currentPage;
                    }
                }
            });
        });

        document.querySelectorAll('.next-page').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer && viewer.currentPage < viewer.totalPages) {
                    viewer.currentPage++;
                    renderPage(viewerId, viewer.currentPage);

                    // Update page number display
                    const container = document.getElementById(viewerId).closest('.tab-pane');
                    const pageDisplay = container.querySelector('.current-page');
                    if (pageDisplay) {
                        pageDisplay.textContent = viewer.currentPage;
                    }
                }
            });
        });

        // Add event listeners for zoom buttons
        document.querySelectorAll('.zoom-in').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer) {
                    // Increase scale by 0.2
                    viewer.scale = (viewer.scale || 1.5) + 0.2;
                    renderPage(viewerId, viewer.currentPage);
                }
            });
        });

        document.querySelectorAll('.zoom-out').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer) {
                    // Decrease scale by 0.2, but don't go below 0.5
                    viewer.scale = Math.max(0.5, (viewer.scale || 1.5) - 0.2);
                    renderPage(viewerId, viewer.currentPage);
                }
            });
        });

        // Initialize PDF viewers
        pdfFiles.forEach((pdf, index) => {
            // Use the correct URL for PDF download
            // The PDF URL should use the stored_filename (with process prefix) if available
            const pdfUrl = pdf.url_path ?
                `${API_URL}${pdf.url_path}` :
                `${API_URL}/download/${pdf.original_filename}`;

            console.log(`Loading PDF from: ${pdfUrl}`);
            initPdfViewer(pdfUrl, `pdf-viewer-${index}`);
        });
    }

    async function initPdfViewer(pdfUrl, containerId) {
        try {
            console.log(`Initializing PDF viewer for ${pdfUrl} in container ${containerId}`);

            // Load the PDF document
            const loadingTask = pdfjsLib.getDocument(pdfUrl);
            const pdf = await loadingTask.promise;

            // Get the first page
            const page = await pdf.getPage(1);

            // Prepare canvas for rendering
            const container = document.getElementById(containerId);
            container.innerHTML = ''; // Clear loading indicator

            // Create a wrapper div for better control of canvas sizing
            const canvasWrapper = document.createElement('div');
            canvasWrapper.className = 'canvas-wrapper';
            container.appendChild(canvasWrapper);

            const canvas = document.createElement('canvas');
            canvasWrapper.appendChild(canvas);

            const context = canvas.getContext('2d');

            // Use a default scale of 1.5
            const scale = 1.5;

            // Create a viewport with the scale
            const viewport = page.getViewport({ scale: scale });

            // Set canvas dimensions to match the viewport
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render the page
            const renderContext = {
                canvasContext: context,
                viewport: viewport
            };

            await page.render(renderContext).promise;

            // Store the PDF instance for later use
            pdfViewers[containerId] = {
                pdf: pdf,
                currentPage: 1,
                totalPages: pdf.numPages,
                scale: scale  // Store the scale for later use
            };

            // Update total pages display
            const tabPane = document.getElementById(containerId).closest('.tab-pane');
            const totalPagesEl = tabPane.querySelector('.total-pages');
            if (totalPagesEl) {
                totalPagesEl.textContent = pdf.numPages;
            }

        } catch (error) {
            console.error('Error initializing PDF viewer:', error);
            document.getElementById(containerId).innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i> Failed to load PDF: ${error.message}
                </div>
            `;
        }
    }

    // Format extracted information to make it more readable
    function formatExtractedInfo(extractedInfo) {
        if (!extractedInfo) return '';

        // Check if the extracted info contains markdown-style formatting
        if (extractedInfo.includes('**')) {
            // Replace markdown bold with HTML bold and add proper formatting
            let formatted = extractedInfo
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // Bold text
                .replace(/\n\n/g, '</div><div class="mb-3">') // Paragraphs
                .replace(/\(Confidence: (high|medium|low)\)/g,
                    (match, confidence) => {
                        const badgeClass = confidence === 'high' ? 'bg-light text-success border border-success' :
                                          confidence === 'medium' ? 'bg-light text-warning border border-warning' : 'bg-light text-danger border border-danger';
                        return `<span class="badge ${badgeClass} ms-2">${confidence}</span>`;
                    });

            // Wrap in a div for proper formatting
            return `<div class="mb-3">${formatted}</div>`;
        }

        // If no markdown formatting, just return as is
        return extractedInfo;
    }

    // Format reasoning text to make it more readable and structured
    function formatReasoningText(reasoning) {
        if (!reasoning) return '';

        // Check if the reasoning contains the DETAILED ANALYSIS marker
        if (reasoning.includes('DETAILED ANALYSIS')) {
            // Split the reasoning into sections
            const sections = reasoning.split('\n\n');

            // Format each section
            const formattedSections = sections.map(section => {
                // Format the DETAILED ANALYSIS header
                if (section.trim().startsWith('DETAILED ANALYSIS')) {
                    return `<h6 class="text-primary mt-3">Detailed Clinical Analysis</h6>`;
                }

                // Format section headers (all caps followed by colon)
                if (/^[A-Z\s]+:/.test(section)) {
                    const headerText = section.split(':')[0];
                    const contentText = section.substring(headerText.length + 1).trim();
                    return `<div class="mt-3">
                        <h6 class="text-primary">${headerText}:</h6>
                        <p>${contentText}</p>
                    </div>`;
                }

                // Highlight clinical values and measurements
                let highlightedSection = section
                    // Highlight numeric values with units
                    .replace(/(\d+(\.\d+)?)\s*(mmHg|mg\/dL|g\/dL|mEq\/L|mmol\/L|%|bpm|kg\/m2|cm|mm|units|mcg|mg|g|mL|L)/g,
                        '<span class="text-primary fw-bold">$&</span>')
                    // Highlight lab values
                    .replace(/(PO2|PCO2|pH|SpO2|FiO2|HCO3|WBC|RBC|Hgb|Hct|Plt|BUN|Cr|Na|K|Cl|Glucose)(\s*)([:=])(\s*)(\d+(\.\d+)?)/g,
                        '<span class="text-primary fw-bold">$1$2$3$4$5</span>')
                    // Highlight clinical terms
                    .replace(/(hypoxemia|hypercapnia|respiratory failure|respiratory acidosis|respiratory alkalosis|metabolic acidosis|metabolic alkalosis|tachypnea|bradypnea|tachycardia|bradycardia|hypertension|hypotension|fever|hypothermia)/gi,
                        '<span class="text-success fw-bold">$&</span>');

                return `<p class="mb-2">${highlightedSection}</p>`;
            });

            return formattedSections.join('');
        }

        // If no special formatting needed, add basic paragraph formatting
        return reasoning.split('\n\n').map(para => `<p class="mb-2">${para}</p>`).join('');
    }

    // PDF.js is now loaded directly in the HTML

    function displayLocalPdfFiles(files) {
        // Create tabs for each PDF/PKL file
        let tabsHtml = '<ul class="nav nav-tabs" id="pdf-tabs" role="tablist">';
        let tabContentHtml = '<div class="tab-content" id="pdf-tabs-content">';

        // Filter out PDF files (we'll only display these)
        const pdfFiles = files.filter(file => file.name.toLowerCase().endsWith('.pdf'));

        if (pdfFiles.length === 0) {
            pdfViewerContent.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i> No PDF files available for viewing. PKL files are used for processing but cannot be displayed in the viewer.
                </div>
            `;
            return;
        }

        pdfFiles.forEach((file, index) => {
            const tabId = `pdf-tab-${index}`;
            const contentId = `pdf-content-${index}`;
            const isActive = index === 0;

            tabsHtml += `
                <li class="nav-item" role="presentation">
                    <button class="nav-link ${isActive ? 'active' : ''}" id="${tabId}" data-bs-toggle="tab"
                            data-bs-target="#${contentId}" type="button" role="tab"
                            aria-controls="${contentId}" aria-selected="${isActive}">
                        ${file.name}
                    </button>
                </li>
            `;

            tabContentHtml += `
                <div class="tab-pane fade ${isActive ? 'show active' : ''}" id="${contentId}" role="tabpanel" aria-labelledby="${tabId}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h5>${file.name}</h5>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="pdf-viewer" id="pdf-viewer-${index}">
                                <div class="text-center p-5">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">Loading PDF...</p>
                                </div>
                            </div>
                            <div class="pdf-navigation d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <button class="btn btn-sm btn-outline-secondary prev-page" data-viewer-id="pdf-viewer-${index}">
                                        <i class="bi bi-chevron-left"></i> Previous
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary next-page ms-2" data-viewer-id="pdf-viewer-${index}">
                                        <i class="bi bi-chevron-right"></i> Next
                                    </button>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <button class="btn btn-sm btn-outline-secondary zoom-out" data-viewer-id="pdf-viewer-${index}">
                                            <i class="bi bi-dash-lg"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary zoom-in ms-1" data-viewer-id="pdf-viewer-${index}">
                                            <i class="bi bi-plus-lg"></i>
                                        </button>
                                    </div>
                                    <div>
                                        Page <span class="current-page">1</span> of <span class="total-pages">...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        tabsHtml += '</ul>';
        tabContentHtml += '</div>';

        pdfViewerContent.innerHTML = tabsHtml + tabContentHtml;

        // Add event listeners for navigation buttons
        document.querySelectorAll('.prev-page').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer && viewer.currentPage > 1) {
                    viewer.currentPage--;
                    renderPage(viewerId, viewer.currentPage);

                    // Update page number display
                    const container = document.getElementById(viewerId).closest('.tab-pane');
                    const pageDisplay = container.querySelector('.current-page');
                    if (pageDisplay) {
                        pageDisplay.textContent = viewer.currentPage;
                    }
                }
            });
        });

        document.querySelectorAll('.next-page').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer && viewer.currentPage < viewer.totalPages) {
                    viewer.currentPage++;
                    renderPage(viewerId, viewer.currentPage);

                    // Update page number display
                    const container = document.getElementById(viewerId).closest('.tab-pane');
                    const pageDisplay = container.querySelector('.current-page');
                    if (pageDisplay) {
                        pageDisplay.textContent = viewer.currentPage;
                    }
                }
            });
        });

        // Add event listeners for zoom buttons
        document.querySelectorAll('.zoom-in').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer) {
                    // Increase scale by 0.2
                    viewer.scale = (viewer.scale || 1.5) + 0.2;
                    renderPage(viewerId, viewer.currentPage);
                }
            });
        });

        document.querySelectorAll('.zoom-out').forEach(button => {
            button.addEventListener('click', function() {
                const viewerId = this.getAttribute('data-viewer-id');
                const viewer = pdfViewers[viewerId];

                if (viewer) {
                    // Decrease scale by 0.2, but don't go below 0.5
                    viewer.scale = Math.max(0.5, (viewer.scale || 1.5) - 0.2);
                    renderPage(viewerId, viewer.currentPage);
                }
            });
        });

        // Initialize PDF viewers using local file URLs (only for PDF files)
        pdfFiles.forEach((file, index) => {
            const fileUrl = URL.createObjectURL(file);
            console.log(`Loading local PDF from: ${fileUrl}`);
            initPdfViewer(fileUrl, `pdf-viewer-${index}`);
        });
    }

    async function renderPage(containerId, pageNumber) {
        try {
            const viewer = pdfViewers[containerId];
            if (!viewer || !viewer.pdf) {
                console.error('PDF viewer not initialized for container:', containerId);
                return;
            }

            const container = document.getElementById(containerId);
            if (!container) {
                console.error('Container not found:', containerId);
                return;
            }

            // Show loading indicator
            container.innerHTML = `
                <div class="text-center p-3">
                    <div class="spinner-border text-primary" role="status" style="width: 1.5rem; height: 1.5rem;"></div>
                    <span class="ms-2">Loading page ${pageNumber}...</span>
                </div>
            `;

            // Get the page
            const page = await viewer.pdf.getPage(pageNumber);

            // Clear loading indicator
            container.innerHTML = '';

            // Create a wrapper div for better control of canvas sizing
            const canvasWrapper = document.createElement('div');
            canvasWrapper.className = 'canvas-wrapper';
            container.appendChild(canvasWrapper);

            // Create canvas for rendering
            const canvas = document.createElement('canvas');
            canvasWrapper.appendChild(canvas);

            const context = canvas.getContext('2d');

            // Use the stored scale or default to 1.5
            const scale = viewer.scale || 1.5;

            // Create a viewport with the scale
            const viewport = page.getViewport({ scale: scale });

            // Set canvas dimensions to match the viewport
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render the page
            const renderContext = {
                canvasContext: context,
                viewport: viewport
            };

            await page.render(renderContext).promise;

            // Update current page in viewer object
            viewer.currentPage = pageNumber;

        } catch (error) {
            console.error('Error rendering PDF page:', error);
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i> Failed to render page ${pageNumber}: ${error.message}
                    </div>
                `;
            }
        }
    }

    // Utility functions
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.querySelector('.main-content').prepend(alertDiv);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 500);
        }, 5000);
    }

    function showSpinner(message, progress = 0) {
        spinnerMessage.textContent = message;
        spinnerProgress.style.width = `${progress}%`;
        spinnerOverlay.style.display = 'flex';
    }

    function updateSpinner(message, progress) {
        spinnerMessage.textContent = message;
        spinnerProgress.style.width = `${progress}%`;
    }

    function hideSpinner() {
        spinnerOverlay.style.display = 'none';
    }

    function downloadJson() {
        if (!extractionResults) return;

        const dataStr = JSON.stringify(extractionResults, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = 'extraction_results.json';

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }
});
